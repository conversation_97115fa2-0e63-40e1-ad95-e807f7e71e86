# Bangladesh Political Violence Map

An interactive web application for visualizing political violence events across Bangladesh. Built with Next.js, TypeScript, Tailwind CSS, and Leaflet.js.

## 🎯 Project Overview

This frontend application provides an interactive map interface for tracking and analyzing political violence events in Bangladesh. The application is designed to be modular and API-ready, allowing for easy integration with a future backend system.

## ✨ Features

### Core Functionality
- **Interactive Map**: Leaflet.js-powered map centered on Bangladesh
- **Event Visualization**: Color-coded markers based on severity levels
- **Event Details**: Rich popups with comprehensive event information
- **Advanced Filtering**: Filter by date range, political parties, divisions, and severity
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices

### UI/UX Features
- **Dark Mode**: Toggle between light and dark themes
- **Animations**: Smooth transitions and pulse effects for recent events
- **Performance Optimized**: Efficient rendering for 100+ markers
- **Accessibility**: Screen reader friendly with proper ARIA labels
- **Mobile-First**: Responsive sidebar and touch-friendly controls

## 🏗️ Architecture

### Project Structure
```
/political-violence-map/
├── frontend/                 # Next.js frontend application
│   ├── src/
│   │   ├── app/             # Next.js App Router pages
│   │   ├── components/      # Reusable UI components
│   │   ├── contexts/        # React Context for state management
│   │   ├── lib/             # Utility functions and API layer
│   │   └── types/           # TypeScript type definitions
│   ├── public/              # Static assets and mock data
│   └── package.json
└── backend/                 # (Future) Backend API directory
```

### Key Components

#### Core Components
- **`MapView`**: Main map component with Leaflet integration
- **`EventMarker`**: Individual event markers with color coding
- **`EventPopup`**: Detailed event information display
- **`FiltersSidebar`**: Advanced filtering interface
- **`DynamicMapView`**: SSR-safe wrapper for map component

#### Data Management
- **`AppContext`**: Global state management with React Context
- **`apiService`**: Modular API layer for data fetching
- **Mock Data**: Realistic Bangladesh political violence events

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd political-violence-map
   ```

2. **Install dependencies**
   ```bash
   cd frontend
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   Navigate to `http://localhost:3000`

### Build for Production
```bash
npm run build
npm start
```

## 📊 Data Structure

### Event Data Format
```typescript
interface ViolenceEvent {
  id: string;
  title: string;
  summary: string;
  location: {
    lat: number;
    lng: number;
    address: string;
    division: string;
    district: string;
  };
  casualties: {
    injured: number;
    dead: number;
  };
  politicalParty: string;
  opposingParty: string;
  date: string; // ISO 8601 format
  severity: 'low' | 'medium' | 'high';
  imageUrl: string;
  source: string;
  verified: boolean;
}
```

## 🔌 API Integration

### Current Implementation
The application currently uses mock data from `/public/events.json`. The API layer is designed for easy backend integration.

### Backend Integration Guide

1. **Update API Configuration**
   ```typescript
   // src/lib/api.ts
   const API_CONFIG = {
     baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
     endpoints: {
       events: '/api/events',
       eventById: '/api/events/:id',
       statistics: '/api/statistics'
     }
   };
   ```

2. **Environment Variables**
   ```bash
   # .env.local
   NEXT_PUBLIC_API_URL=http://your-backend-url
   ```

3. **API Endpoints to Implement**
   - `GET /api/events` - Fetch all events with optional filters
   - `GET /api/events/:id` - Fetch single event
   - `GET /api/statistics` - Fetch dashboard statistics
   - `GET /api/divisions` - Fetch available divisions/districts

### Expected API Response Format
```json
{
  "events": [/* array of ViolenceEvent objects */],
  "metadata": {
    "totalEvents": 100,
    "lastUpdated": "2024-01-01T00:00:00Z",
    "divisions": ["Dhaka", "Chittagong", ...],
    "politicalParties": ["Awami League", "BNP", ...]
  }
}
```

## 🎨 Customization

### Styling
- **Tailwind CSS**: Utility-first CSS framework
- **Custom CSS**: Additional styles in `src/app/globals.css`
- **Dark Mode**: Automatic system preference detection with manual toggle

### Map Configuration
```typescript
// src/types/index.ts
export const BANGLADESH_BOUNDS = {
  north: 26.6382,
  south: 20.7404,
  east: 92.6727,
  west: 88.0844
};

export const BANGLADESH_CENTER: [number, number] = [23.6850, 90.3563];
```

### Color Scheme
```typescript
const SEVERITY_COLORS = {
  high: '#ef4444',    // red-500
  medium: '#f97316',  // orange-500
  low: '#eab308',     // yellow-500
};
```

## 📱 Responsive Design

### Breakpoints
- **Mobile**: < 768px (collapsible sidebar, touch-optimized)
- **Tablet**: 768px - 1024px (adaptive layout)
- **Desktop**: > 1024px (full sidebar, hover effects)

### Mobile Features
- Collapsible sidebar with overlay
- Touch-friendly map controls
- Optimized popup sizes
- Swipe gestures support

## 🔧 Performance Optimizations

### Map Performance
- **Dynamic Loading**: Map components loaded client-side only
- **Marker Clustering**: Efficient rendering for large datasets
- **Viewport Optimization**: Only render visible markers
- **Lazy Loading**: Images loaded on demand

### Bundle Optimization
- **Code Splitting**: Automatic route-based splitting
- **Tree Shaking**: Unused code elimination
- **Image Optimization**: Next.js automatic image optimization

## 🧪 Testing

### Manual Testing Checklist
- [ ] Map loads correctly on all devices
- [ ] Filters work and update map in real-time
- [ ] Dark mode toggle functions properly
- [ ] Event popups display complete information
- [ ] Responsive design works on mobile/tablet
- [ ] Performance is acceptable with 100+ markers

### Future Testing Setup
```bash
# Install testing dependencies
npm install --save-dev @testing-library/react @testing-library/jest-dom jest

# Run tests
npm test
```

## 🚀 Deployment

### Vercel (Recommended)
```bash
npm install -g vercel
vercel
```

### Docker
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Leaflet.js**: Open-source mapping library
- **Next.js**: React framework for production
- **Tailwind CSS**: Utility-first CSS framework
- **Lucide React**: Beautiful icon library
- **OpenStreetMap**: Map data provider
