# Project Structure Overview

## Directory Structure

```
/political-violence-map/
├── README.md                           # Main project documentation
├── PROJECT_STRUCTURE.md               # This file
├── frontend/                           # Next.js frontend application
│   ├── src/
│   │   ├── app/                       # Next.js App Router
│   │   │   ├── layout.tsx             # Root layout with <PERSON><PERSON><PERSON><PERSON><PERSON>
│   │   │   ├── page.tsx               # Main page component
│   │   │   ├── globals.css            # Global styles and custom CSS
│   │   │   └── favicon.ico            # App icon
│   │   ├── components/                # Reusable UI components
│   │   │   ├── MapView.tsx            # Main map component with Leaflet
│   │   │   ├── DynamicMapView.tsx     # SSR-safe map wrapper
│   │   │   ├── EventMarker.tsx        # Individual event markers
│   │   │   ├── EventPopup.tsx         # Event detail popups
│   │   │   └── FiltersSidebar.tsx     # Advanced filtering interface
│   │   ├── contexts/                  # React Context providers
│   │   │   └── AppContext.tsx         # Global state management
│   │   ├── lib/                       # Utility functions and services
│   │   │   └── api.ts                 # API service layer
│   │   └── types/                     # TypeScript type definitions
│   │       └── index.ts               # All type definitions
│   ├── public/                        # Static assets
│   │   └── events.json                # Mock data for development
│   ├── API_INTEGRATION.md             # Backend integration guide
│   ├── TESTING.md                     # Testing procedures and checklist
│   ├── package.json                   # Dependencies and scripts
│   ├── tsconfig.json                  # TypeScript configuration
│   ├── tailwind.config.ts             # Tailwind CSS configuration
│   ├── next.config.ts                 # Next.js configuration
│   └── eslint.config.mjs              # ESLint configuration
└── backend/                           # (Future) Backend API directory
    └── (empty - ready for backend implementation)
```

## Component Architecture

### Core Components

#### 1. **MapView** (`src/components/MapView.tsx`)
- **Purpose**: Main interactive map component
- **Dependencies**: Leaflet.js, react-leaflet
- **Features**:
  - Bangladesh-centered map with bounds restriction
  - Event marker rendering
  - Map controls and interactions
  - Performance optimizations for large datasets

#### 2. **DynamicMapView** (`src/components/DynamicMapView.tsx`)
- **Purpose**: SSR-safe wrapper for MapView
- **Features**:
  - Dynamic import to prevent server-side rendering issues
  - Loading state management
  - Error boundary protection

#### 3. **EventMarker** (`src/components/EventMarker.tsx`)
- **Purpose**: Individual event markers on the map
- **Features**:
  - Color-coded by severity level
  - Hover animations and effects
  - Click handling for popup display
  - Recent event pulse animation

#### 4. **EventPopup** (`src/components/EventPopup.tsx`)
- **Purpose**: Detailed event information display
- **Features**:
  - Rich event information layout
  - Image display with fallbacks
  - Casualty information formatting
  - Verification status indicators

#### 5. **FiltersSidebar** (`src/components/FiltersSidebar.tsx`)
- **Purpose**: Advanced filtering interface
- **Features**:
  - Date range selection
  - Multi-select filters (parties, divisions, severity)
  - Real-time filter application
  - Mobile-responsive design

### State Management

#### **AppContext** (`src/contexts/AppContext.tsx`)
- **Purpose**: Global application state management
- **State Includes**:
  - Event data and filtered results
  - UI state (sidebar, dark mode, loading)
  - Filter configurations
  - Selected event tracking

### Data Layer

#### **API Service** (`src/lib/api.ts`)
- **Purpose**: Centralized data fetching and management
- **Features**:
  - Mock data integration (development)
  - Backend-ready architecture
  - Error handling and retry logic
  - Filtering and pagination support

#### **Type Definitions** (`src/types/index.ts`)
- **Purpose**: TypeScript type safety
- **Includes**:
  - Event data structures
  - Filter configurations
  - Geographic boundaries
  - API response formats

## Data Flow

```
User Interaction
       ↓
   Component
       ↓
   AppContext (State Management)
       ↓
   API Service
       ↓
   Mock Data / Backend API
       ↓
   Component Re-render
```

## Key Features Implementation

### 1. **Interactive Map**
- **Technology**: Leaflet.js with react-leaflet
- **Location**: `src/components/MapView.tsx`
- **Features**: Pan, zoom, marker clustering, bounds restriction

### 2. **Event Visualization**
- **Technology**: Custom SVG markers with CSS animations
- **Location**: `src/components/EventMarker.tsx`
- **Features**: Color coding, hover effects, selection states

### 3. **Advanced Filtering**
- **Technology**: React state with custom hooks
- **Location**: `src/components/FiltersSidebar.tsx`
- **Features**: Multi-criteria filtering, real-time updates

### 4. **Dark Mode**
- **Technology**: Tailwind CSS with system preference detection
- **Location**: `src/contexts/AppContext.tsx`
- **Features**: Toggle, persistence, system sync

### 5. **Responsive Design**
- **Technology**: Tailwind CSS with mobile-first approach
- **Implementation**: All components
- **Features**: Collapsible sidebar, touch-friendly controls

## Performance Optimizations

### 1. **Code Splitting**
- Dynamic imports for map components
- Route-based splitting with Next.js
- Lazy loading of heavy dependencies

### 2. **Map Performance**
- Marker clustering for large datasets
- Viewport-based rendering
- Efficient re-rendering strategies

### 3. **Bundle Optimization**
- Tree shaking for unused code
- Image optimization with Next.js
- CSS purging with Tailwind

## Development Workflow

### 1. **Local Development**
```bash
cd frontend
npm install
npm run dev
```

### 2. **Type Checking**
```bash
npm run type-check
```

### 3. **Linting**
```bash
npm run lint
npm run lint:fix
```

### 4. **Building**
```bash
npm run build
npm start
```

## Configuration Files

### 1. **Next.js** (`next.config.ts`)
- TypeScript support
- Image optimization
- Build optimizations

### 2. **Tailwind CSS** (`tailwind.config.ts`)
- Custom color schemes
- Dark mode configuration
- Component-specific utilities

### 3. **TypeScript** (`tsconfig.json`)
- Strict type checking
- Path aliases (@/* imports)
- Modern ES features

### 4. **ESLint** (`eslint.config.mjs`)
- Next.js recommended rules
- TypeScript integration
- Custom rule configurations

## Future Enhancements

### 1. **Backend Integration**
- Real-time data updates
- User authentication
- Data persistence

### 2. **Advanced Features**
- Heat map visualization
- Timeline analysis
- Export functionality

### 3. **Performance**
- Service worker for offline support
- Progressive Web App features
- Advanced caching strategies

### 4. **Analytics**
- User interaction tracking
- Performance monitoring
- Error reporting

## Best Practices Implemented

### 1. **Code Organization**
- Modular component structure
- Separation of concerns
- Reusable utility functions

### 2. **Type Safety**
- Comprehensive TypeScript coverage
- Strict type checking
- Interface-driven development

### 3. **Performance**
- Lazy loading strategies
- Efficient state management
- Optimized rendering

### 4. **Accessibility**
- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support

### 5. **Maintainability**
- Clear documentation
- Consistent code style
- Comprehensive error handling
