@import "tailwindcss";
@import 'leaflet/dist/leaflet.css';

:root {
    --background: #ffffff;
    --foreground: #171717;
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --font-sans: var(--font-geist-sans);
    --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
    :root {
        --background: #0a0a0a;
        --foreground: #ededed;
    }
}

body {
    background: var(--background);
    color: var(--foreground);
    font-family: Arial, Helvetica, sans-serif;
}

/* Custom styles for the map and components */

/* Leaflet map custom styles */
.leaflet-container {
    font-family: inherit;
}

.leaflet-popup-content-wrapper {
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.leaflet-popup-content {
    margin: 0;
    padding: 0;
}

.leaflet-popup-tip {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Custom marker animations */
.custom-marker-icon {
    transition: all 0.2s ease-in-out;
}

.custom-marker-icon:hover {
    transform: scale(1.1);
}

/* Pulse animation for recent events */
@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom popup styles */
.custom-popup .leaflet-popup-content-wrapper {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dark .custom-popup .leaflet-popup-content-wrapper {
    background: #1f2937;
    color: #f9fafb;
}

.custom-popup .leaflet-popup-tip {
    background: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.dark .custom-popup .leaflet-popup-tip {
    background: #1f2937;
}

/* Smooth transitions for dark mode */
* {
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

/* Custom scrollbar for sidebar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.dark .custom-scrollbar::-webkit-scrollbar-track {
    background: #374151;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #6b7280;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Loading animation */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Fade in animation for components */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .leaflet-popup-content-wrapper {
        max-width: 280px !important;
    }

    .leaflet-control-zoom {
        margin-left: 10px !important;
        margin-top: 60px !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .custom-marker-icon circle {
        stroke-width: 3px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

    .animate-pulse,
    .animate-spin,
    .animate-fade-in,
    .custom-marker-icon {
        animation: none !important;
        transition: none !important;
    }
}