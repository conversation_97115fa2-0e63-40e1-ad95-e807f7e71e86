module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/lib/api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "apiService": ()=>apiService,
    "default": ()=>__TURBOPACK__default__export__
});
// Configuration for API endpoints
const API_CONFIG = {
    // For development, use mock data from public folder
    // In production, this would be your backend API URL
    baseURL: ("TURBOPACK compile-time falsy", 0) ? "TURBOPACK unreachable" : '',
    endpoints: {
        events: '/events.json'
    }
};
class ApiService {
    baseURL;
    constructor(){
        this.baseURL = API_CONFIG.baseURL;
    }
    /**
   * Fetch all violence events
   * In the future, this will connect to a real backend API
   */ async getEvents() {
        try {
            const response = await fetch(`${this.baseURL}${API_CONFIG.endpoints.events}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch events: ${response.statusText}`);
            }
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error fetching events:', error);
            throw new Error('Failed to load violence events data');
        }
    }
    /**
   * Get a single event by ID
   * Currently filters from all events, but will be a separate API call in the future
   */ async getEventById(id) {
        try {
            const data = await this.getEvents();
            const event = data.events.find((event)=>event.id === id);
            return event || null;
        } catch (error) {
            console.error('Error fetching event by ID:', error);
            return null;
        }
    }
    /**
   * Filter events based on criteria
   * Currently done client-side, but will be server-side in the future
   */ async getFilteredEvents(filters) {
        try {
            const data = await this.getEvents();
            let filteredEvents = data.events;
            // Apply date range filter
            if (filters.dateRange?.start || filters.dateRange?.end) {
                filteredEvents = filteredEvents.filter((event)=>{
                    const eventDate = new Date(event.date);
                    const start = filters.dateRange?.start;
                    const end = filters.dateRange?.end;
                    if (start && eventDate < start) return false;
                    if (end && eventDate > end) return false;
                    return true;
                });
            }
            // Apply political party filter
            if (filters.politicalParties && filters.politicalParties.length > 0) {
                filteredEvents = filteredEvents.filter((event)=>filters.politicalParties.includes(event.politicalParty) || filters.politicalParties.includes(event.opposingParty));
            }
            // Apply division filter
            if (filters.divisions && filters.divisions.length > 0) {
                filteredEvents = filteredEvents.filter((event)=>filters.divisions.includes(event.location.division));
            }
            // Apply district filter
            if (filters.districts && filters.districts.length > 0) {
                filteredEvents = filteredEvents.filter((event)=>filters.districts.includes(event.location.district));
            }
            // Apply severity filter
            if (filters.severityLevels && filters.severityLevels.length > 0) {
                filteredEvents = filteredEvents.filter((event)=>filters.severityLevels.includes(event.severity));
            }
            // Apply verified only filter
            if (filters.verifiedOnly) {
                filteredEvents = filteredEvents.filter((event)=>event.verified);
            }
            return filteredEvents;
        } catch (error) {
            console.error('Error filtering events:', error);
            throw new Error('Failed to filter events');
        }
    }
    /**
   * Get events within a geographic bounding box
   * Useful for map viewport optimization
   */ async getEventsInBounds(bounds) {
        try {
            const data = await this.getEvents();
            return data.events.filter((event)=>{
                const { lat, lng } = event.location;
                return lat <= bounds.north && lat >= bounds.south && lng <= bounds.east && lng >= bounds.west;
            });
        } catch (error) {
            console.error('Error fetching events in bounds:', error);
            return [];
        }
    }
    /**
   * Get statistics for dashboard/summary views
   * Will be a separate API endpoint in the future
   */ async getStatistics() {
        try {
            const data = await this.getEvents();
            const events = data.events;
            const stats = {
                totalEvents: events.length,
                totalInjured: events.reduce((sum, event)=>sum + event.casualties.injured, 0),
                totalDeaths: events.reduce((sum, event)=>sum + event.casualties.dead, 0),
                byDivision: {},
                bySeverity: {},
                byParty: {},
                recentEvents: events.sort((a, b)=>new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 5)
            };
            // Calculate division statistics
            events.forEach((event)=>{
                stats.byDivision[event.location.division] = (stats.byDivision[event.location.division] || 0) + 1;
            });
            // Calculate severity statistics
            events.forEach((event)=>{
                stats.bySeverity[event.severity] = (stats.bySeverity[event.severity] || 0) + 1;
            });
            // Calculate party statistics
            events.forEach((event)=>{
                stats.byParty[event.politicalParty] = (stats.byParty[event.politicalParty] || 0) + 1;
            });
            return stats;
        } catch (error) {
            console.error('Error fetching statistics:', error);
            throw new Error('Failed to load statistics');
        }
    }
}
const apiService = new ApiService();
const __TURBOPACK__default__export__ = ApiService;
}),
"[project]/src/contexts/AppContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AppProvider": ()=>AppProvider,
    "default": ()=>__TURBOPACK__default__export__,
    "useApp": ()=>useApp
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
// Initial state
const initialState = {
    events: [],
    filteredEvents: [],
    eventsData: null,
    selectedEvent: null,
    loading: true,
    error: null,
    sidebarOpen: false,
    darkMode: false,
    filters: {
        dateRange: {
            start: null,
            end: null
        },
        politicalParties: [],
        divisions: [],
        districts: [],
        severityLevels: [],
        verifiedOnly: false
    },
    availableParties: [],
    availableDistricts: []
};
// Reducer function
function appReducer(state, action) {
    switch(action.type){
        case 'SET_LOADING':
            return {
                ...state,
                loading: action.payload
            };
        case 'SET_ERROR':
            return {
                ...state,
                error: action.payload,
                loading: false
            };
        case 'SET_EVENTS_DATA':
            const eventsData = action.payload;
            const availableParties = Array.from(new Set([
                ...eventsData.events.map((e)=>e.politicalParty),
                ...eventsData.events.map((e)=>e.opposingParty)
            ])).filter(Boolean).sort();
            const availableDistricts = Array.from(new Set(eventsData.events.map((e)=>e.location.district))).sort();
            return {
                ...state,
                eventsData,
                events: eventsData.events,
                filteredEvents: eventsData.events,
                availableParties,
                availableDistricts,
                loading: false,
                error: null
            };
        case 'SET_FILTERED_EVENTS':
            return {
                ...state,
                filteredEvents: action.payload
            };
        case 'SET_SELECTED_EVENT':
            return {
                ...state,
                selectedEvent: action.payload
            };
        case 'SET_SIDEBAR_OPEN':
            return {
                ...state,
                sidebarOpen: action.payload
            };
        case 'SET_DARK_MODE':
            return {
                ...state,
                darkMode: action.payload
            };
        case 'SET_FILTERS':
            return {
                ...state,
                filters: action.payload
            };
        case 'TOGGLE_SIDEBAR':
            return {
                ...state,
                sidebarOpen: !state.sidebarOpen
            };
        case 'TOGGLE_DARK_MODE':
            return {
                ...state,
                darkMode: !state.darkMode
            };
        default:
            return state;
    }
}
// Context
const AppContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(null);
function AppProvider({ children }) {
    const [state, dispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useReducer"])(appReducer, initialState);
    // Load events from API
    const loadEvents = async ()=>{
        try {
            dispatch({
                type: 'SET_LOADING',
                payload: true
            });
            dispatch({
                type: 'SET_ERROR',
                payload: null
            });
            const eventsData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiService"].getEvents();
            dispatch({
                type: 'SET_EVENTS_DATA',
                payload: eventsData
            });
        } catch (error) {
            console.error('Failed to load events:', error);
            dispatch({
                type: 'SET_ERROR',
                payload: error instanceof Error ? error.message : 'Failed to load events'
            });
        }
    };
    // Apply filters and get filtered events
    const applyFilters = async (filters)=>{
        try {
            dispatch({
                type: 'SET_FILTERS',
                payload: filters
            });
            const filteredEvents = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiService"].getFilteredEvents(filters);
            dispatch({
                type: 'SET_FILTERED_EVENTS',
                payload: filteredEvents
            });
            // Clear selected event if it's not in filtered results
            if (state.selectedEvent && !filteredEvents.find((e)=>e.id === state.selectedEvent.id)) {
                dispatch({
                    type: 'SET_SELECTED_EVENT',
                    payload: null
                });
            }
        } catch (error) {
            console.error('Failed to apply filters:', error);
            dispatch({
                type: 'SET_ERROR',
                payload: 'Failed to apply filters'
            });
        }
    };
    // Select an event
    const selectEvent = (event)=>{
        dispatch({
            type: 'SET_SELECTED_EVENT',
            payload: event
        });
    };
    // Toggle sidebar
    const toggleSidebar = ()=>{
        dispatch({
            type: 'TOGGLE_SIDEBAR'
        });
    };
    // Set sidebar open state
    const setSidebarOpen = (open)=>{
        dispatch({
            type: 'SET_SIDEBAR_OPEN',
            payload: open
        });
    };
    // Toggle dark mode
    const toggleDarkMode = ()=>{
        dispatch({
            type: 'TOGGLE_DARK_MODE'
        });
        // Persist dark mode preference
        const newDarkMode = !state.darkMode;
        localStorage.setItem('darkMode', JSON.stringify(newDarkMode));
        // Update document class
        if (newDarkMode) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    };
    // Load events on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        loadEvents();
    }, []);
    // Initialize dark mode from localStorage
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const savedDarkMode = localStorage.getItem('darkMode');
        if (savedDarkMode) {
            const isDark = JSON.parse(savedDarkMode);
            dispatch({
                type: 'SET_DARK_MODE',
                payload: isDark
            });
            if (isDark) {
                document.documentElement.classList.add('dark');
            }
        }
    }, []);
    // Actions object
    const actions = {
        loadEvents,
        applyFilters,
        selectEvent,
        toggleSidebar,
        toggleDarkMode,
        setSidebarOpen
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AppContext.Provider, {
        value: {
            state,
            dispatch,
            actions
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AppContext.tsx",
        lineNumber: 239,
        columnNumber: 5
    }, this);
}
function useApp() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AppContext);
    if (!context) {
        throw new Error('useApp must be used within an AppProvider');
    }
    return context;
}
const __TURBOPACK__default__export__ = AppContext;
}),
"[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
;
else {
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ("TURBOPACK compile-time truthy", 1) {
                module.exports = __turbopack_context__.r("[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)");
            } else //TURBOPACK unreachable
            ;
        } else //TURBOPACK unreachable
        ;
    }
} //# sourceMappingURL=module.compiled.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].React; //# sourceMappingURL=react.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__bd96961d._.js.map