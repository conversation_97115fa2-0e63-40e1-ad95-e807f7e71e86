{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/types/index.ts"], "sourcesContent": ["export interface Location {\n  lat: number;\n  lng: number;\n  address: string;\n  division: string;\n  district: string;\n}\n\nexport interface Casualties {\n  injured: number;\n  dead: number;\n}\n\nexport type SeverityLevel = 'low' | 'medium' | 'high';\n\nexport interface ViolenceEvent {\n  id: string;\n  title: string;\n  summary: string;\n  location: Location;\n  casualties: Casualties;\n  politicalParty: string;\n  opposingParty: string;\n  date: string; // ISO 8601 format\n  severity: SeverityLevel;\n  imageUrl: string;\n  source: string;\n  verified: boolean;\n}\n\nexport interface EventsMetadata {\n  totalEvents: number;\n  lastUpdated: string;\n  divisions: string[];\n  politicalParties: string[];\n  severityLevels: Record<SeverityLevel, string>;\n}\n\nexport interface EventsData {\n  events: ViolenceEvent[];\n  metadata: EventsMetadata;\n}\n\nexport interface FilterOptions {\n  dateRange: {\n    start: Date | null;\n    end: Date | null;\n  };\n  politicalParties: string[];\n  divisions: string[];\n  districts: string[];\n  severityLevels: SeverityLevel[];\n  verifiedOnly: boolean;\n}\n\nexport interface MapBounds {\n  north: number;\n  south: number;\n  east: number;\n  west: number;\n}\n\n// Bangladesh administrative divisions\nexport const BANGLADESH_DIVISIONS = [\n  'Dhaka',\n  'Chittagong', \n  'Sylhet',\n  'Rajshahi',\n  'Barisal',\n  'Rangpur',\n  'Mymensingh',\n  'Khulna'\n] as const;\n\n// Bangladesh map bounds\nexport const BANGLADESH_BOUNDS: MapBounds = {\n  north: 26.6382,\n  south: 20.7404,\n  east: 92.6727,\n  west: 88.0844\n};\n\n// Default map center (approximately center of Bangladesh)\nexport const BANGLADESH_CENTER: [number, number] = [23.6850, 90.3563];\n"], "names": [], "mappings": ";;;;;AA+DO,MAAM,uBAAuB;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,oBAA+B;IAC1C,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;AACR;AAGO,MAAM,oBAAsC;IAAC;IAAS;CAAQ", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/components/FiltersSidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { BANGLADESH_DIVISIONS, FilterOptions, SeverityLevel } from \"@/types\";\nimport {\n    AlertTriangle,\n    Calendar,\n    CheckCircle,\n    Filter,\n    MapPin,\n    RotateCcw,\n    Users,\n    X,\n} from \"lucide-react\";\nimport { useEffect, useState } from \"react\";\n\ninterface FiltersSidebarProps {\n    filters: FilterOptions;\n    onFiltersChange: (filters: FilterOptions) => void;\n    availableParties: string[];\n    availableDistricts: string[];\n    isOpen: boolean;\n    onToggle: () => void;\n    className?: string;\n}\n\nexport default function FiltersSidebar({\n    filters,\n    onFiltersChange,\n    availableParties,\n    availableDistricts,\n    isOpen,\n    onToggle,\n    className = \"\",\n}: FiltersSidebarProps) {\n    const [localFilters, setLocalFilters] = useState<FilterOptions>(filters);\n\n    // Update local filters when props change\n    useEffect(() => {\n        setLocalFilters(filters);\n    }, [filters]);\n\n    // Apply filters\n    const applyFilters = () => {\n        onFiltersChange(localFilters);\n    };\n\n    // Reset all filters\n    const resetFilters = () => {\n        const resetFilters: FilterOptions = {\n            dateRange: { start: null, end: null },\n            politicalParties: [],\n            divisions: [],\n            districts: [],\n            severityLevels: [],\n            verifiedOnly: false,\n        };\n        setLocalFilters(resetFilters);\n        onFiltersChange(resetFilters);\n    };\n\n    // Handle date range changes\n    const handleDateRangeChange = (field: \"start\" | \"end\", value: string) => {\n        setLocalFilters((prev) => ({\n            ...prev,\n            dateRange: {\n                ...prev.dateRange,\n                [field]: value ? new Date(value) : null,\n            },\n        }));\n    };\n\n    // Handle multi-select changes\n    const handleMultiSelectChange = (\n        field: keyof Pick<\n            FilterOptions,\n            \"politicalParties\" | \"divisions\" | \"districts\" | \"severityLevels\"\n        >,\n        value: string\n    ) => {\n        setLocalFilters((prev) => {\n            const currentValues = prev[field] as string[];\n            const newValues = currentValues.includes(value)\n                ? currentValues.filter((v) => v !== value)\n                : [...currentValues, value];\n\n            return {\n                ...prev,\n                [field]: newValues,\n            };\n        });\n    };\n\n    // Count active filters\n    const activeFiltersCount =\n        (localFilters.dateRange.start || localFilters.dateRange.end ? 1 : 0) +\n        localFilters.politicalParties.length +\n        localFilters.divisions.length +\n        localFilters.districts.length +\n        localFilters.severityLevels.length +\n        (localFilters.verifiedOnly ? 1 : 0);\n\n    return (\n        <>\n            {/* Mobile overlay */}\n            {isOpen && (\n                <div\n                    className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n                    onClick={onToggle}\n                />\n            )}\n\n            {/* Sidebar */}\n            <div\n                className={`\n        fixed lg:relative top-0 left-0 h-full bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 z-50\n        transform transition-transform duration-300 ease-in-out\n        ${isOpen ? \"translate-x-0\" : \"-translate-x-full lg:translate-x-0\"}\n        w-80 flex flex-col\n        ${className}\n      `}\n            >\n                {/* Header */}\n                <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\n                    <div className=\"flex items-center space-x-2\">\n                        <Filter className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" />\n                        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\n                            Filters\n                        </h2>\n                        {activeFiltersCount > 0 && (\n                            <span className=\"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded-full\">\n                                {activeFiltersCount}\n                            </span>\n                        )}\n                    </div>\n                    <button\n                        onClick={onToggle}\n                        className=\"lg:hidden p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800\"\n                    >\n                        <X className=\"w-5 h-5\" />\n                    </button>\n                </div>\n\n                {/* Filters content */}\n                <div className=\"flex-1 overflow-y-auto custom-scrollbar p-4 space-y-6\">\n                    {/* Date Range Filter */}\n                    <div className=\"space-y-3\">\n                        <div className=\"flex items-center space-x-2\">\n                            <Calendar className=\"w-4 h-4 text-gray-600 dark:text-gray-400\" />\n                            <h3 className=\"font-medium text-gray-900 dark:text-gray-100\">\n                                Date Range\n                            </h3>\n                        </div>\n                        <div className=\"space-y-2\">\n                            <div>\n                                <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n                                    From\n                                </label>\n                                <input\n                                    type=\"date\"\n                                    value={\n                                        localFilters.dateRange.start\n                                            ? localFilters.dateRange.start\n                                                  .toISOString()\n                                                  .split(\"T\")[0]\n                                            : \"\"\n                                    }\n                                    onChange={(e) =>\n                                        handleDateRangeChange(\n                                            \"start\",\n                                            e.target.value\n                                        )\n                                    }\n                                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100\"\n                                />\n                            </div>\n                            <div>\n                                <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n                                    To\n                                </label>\n                                <input\n                                    type=\"date\"\n                                    value={\n                                        localFilters.dateRange.end\n                                            ? localFilters.dateRange.end\n                                                  .toISOString()\n                                                  .split(\"T\")[0]\n                                            : \"\"\n                                    }\n                                    onChange={(e) =>\n                                        handleDateRangeChange(\n                                            \"end\",\n                                            e.target.value\n                                        )\n                                    }\n                                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100\"\n                                />\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* Political Parties Filter */}\n                    <div className=\"space-y-3\">\n                        <div className=\"flex items-center space-x-2\">\n                            <Users className=\"w-4 h-4 text-gray-600 dark:text-gray-400\" />\n                            <h3 className=\"font-medium text-gray-900 dark:text-gray-100\">\n                                Political Parties\n                            </h3>\n                        </div>\n                        <div className=\"space-y-2 max-h-40 overflow-y-auto\">\n                            {availableParties.map((party) => (\n                                <label\n                                    key={party}\n                                    className=\"flex items-center space-x-2 cursor-pointer\"\n                                >\n                                    <input\n                                        type=\"checkbox\"\n                                        checked={localFilters.politicalParties.includes(\n                                            party\n                                        )}\n                                        onChange={() =>\n                                            handleMultiSelectChange(\n                                                \"politicalParties\",\n                                                party\n                                            )\n                                        }\n                                        className=\"rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500\"\n                                    />\n                                    <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                                        {party}\n                                    </span>\n                                </label>\n                            ))}\n                        </div>\n                    </div>\n\n                    {/* Divisions Filter */}\n                    <div className=\"space-y-3\">\n                        <div className=\"flex items-center space-x-2\">\n                            <MapPin className=\"w-4 h-4 text-gray-600 dark:text-gray-400\" />\n                            <h3 className=\"font-medium text-gray-900 dark:text-gray-100\">\n                                Divisions\n                            </h3>\n                        </div>\n                        <div className=\"space-y-2\">\n                            {BANGLADESH_DIVISIONS.map((division) => (\n                                <label\n                                    key={division}\n                                    className=\"flex items-center space-x-2 cursor-pointer\"\n                                >\n                                    <input\n                                        type=\"checkbox\"\n                                        checked={localFilters.divisions.includes(\n                                            division\n                                        )}\n                                        onChange={() =>\n                                            handleMultiSelectChange(\n                                                \"divisions\",\n                                                division\n                                            )\n                                        }\n                                        className=\"rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500\"\n                                    />\n                                    <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                                        {division}\n                                    </span>\n                                </label>\n                            ))}\n                        </div>\n                    </div>\n\n                    {/* Severity Levels Filter */}\n                    <div className=\"space-y-3\">\n                        <div className=\"flex items-center space-x-2\">\n                            <AlertTriangle className=\"w-4 h-4 text-gray-600 dark:text-gray-400\" />\n                            <h3 className=\"font-medium text-gray-900 dark:text-gray-100\">\n                                Severity\n                            </h3>\n                        </div>\n                        <div className=\"space-y-2\">\n                            {([\"high\", \"medium\", \"low\"] as SeverityLevel[]).map(\n                                (level) => (\n                                    <label\n                                        key={level}\n                                        className=\"flex items-center space-x-2 cursor-pointer\"\n                                    >\n                                        <input\n                                            type=\"checkbox\"\n                                            checked={localFilters.severityLevels.includes(\n                                                level\n                                            )}\n                                            onChange={() =>\n                                                handleMultiSelectChange(\n                                                    \"severityLevels\",\n                                                    level\n                                                )\n                                            }\n                                            className=\"rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500\"\n                                        />\n                                        <span className=\"text-sm text-gray-700 dark:text-gray-300 capitalize\">\n                                            {level}\n                                        </span>\n                                    </label>\n                                )\n                            )}\n                        </div>\n                    </div>\n\n                    {/* Verified Only Filter */}\n                    <div className=\"space-y-3\">\n                        <div className=\"flex items-center space-x-2\">\n                            <CheckCircle className=\"w-4 h-4 text-gray-600 dark:text-gray-400\" />\n                            <h3 className=\"font-medium text-gray-900 dark:text-gray-100\">\n                                Verification\n                            </h3>\n                        </div>\n                        <label className=\"flex items-center space-x-2 cursor-pointer\">\n                            <input\n                                type=\"checkbox\"\n                                checked={localFilters.verifiedOnly}\n                                onChange={(e) =>\n                                    setLocalFilters((prev) => ({\n                                        ...prev,\n                                        verifiedOnly: e.target.checked,\n                                    }))\n                                }\n                                className=\"rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500\"\n                            />\n                            <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                                Verified events only\n                            </span>\n                        </label>\n                    </div>\n                </div>\n\n                {/* Footer with action buttons */}\n                <div className=\"p-4 border-t border-gray-200 dark:border-gray-700 space-y-2\">\n                    <button\n                        onClick={applyFilters}\n                        className=\"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md text-sm font-medium transition-colors\"\n                    >\n                        Apply Filters\n                    </button>\n                    <button\n                        onClick={resetFilters}\n                        className=\"w-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-md text-sm font-medium transition-colors flex items-center justify-center space-x-2\"\n                    >\n                        <RotateCcw className=\"w-4 h-4\" />\n                        <span>Reset All</span>\n                    </button>\n                </div>\n            </div>\n        </>\n    );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAbA;;;;;AAyBe,SAAS,eAAe,EACnC,OAAO,EACP,eAAe,EACf,gBAAgB,EAChB,kBAAkB,EAClB,MAAM,EACN,QAAQ,EACR,YAAY,EAAE,EACI;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,gBAAgB;IACpB,GAAG;QAAC;KAAQ;IAEZ,gBAAgB;IAChB,MAAM,eAAe;QACjB,gBAAgB;IACpB;IAEA,oBAAoB;IACpB,MAAM,eAAe;QACjB,MAAM,eAA8B;YAChC,WAAW;gBAAE,OAAO;gBAAM,KAAK;YAAK;YACpC,kBAAkB,EAAE;YACpB,WAAW,EAAE;YACb,WAAW,EAAE;YACb,gBAAgB,EAAE;YAClB,cAAc;QAClB;QACA,gBAAgB;QAChB,gBAAgB;IACpB;IAEA,4BAA4B;IAC5B,MAAM,wBAAwB,CAAC,OAAwB;QACnD,gBAAgB,CAAC,OAAS,CAAC;gBACvB,GAAG,IAAI;gBACP,WAAW;oBACP,GAAG,KAAK,SAAS;oBACjB,CAAC,MAAM,EAAE,QAAQ,IAAI,KAAK,SAAS;gBACvC;YACJ,CAAC;IACL;IAEA,8BAA8B;IAC9B,MAAM,0BAA0B,CAC5B,OAIA;QAEA,gBAAgB,CAAC;YACb,MAAM,gBAAgB,IAAI,CAAC,MAAM;YACjC,MAAM,YAAY,cAAc,QAAQ,CAAC,SACnC,cAAc,MAAM,CAAC,CAAC,IAAM,MAAM,SAClC;mBAAI;gBAAe;aAAM;YAE/B,OAAO;gBACH,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACb;QACJ;IACJ;IAEA,uBAAuB;IACvB,MAAM,qBACF,CAAC,aAAa,SAAS,CAAC,KAAK,IAAI,aAAa,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC,IACnE,aAAa,gBAAgB,CAAC,MAAM,GACpC,aAAa,SAAS,CAAC,MAAM,GAC7B,aAAa,SAAS,CAAC,MAAM,GAC7B,aAAa,cAAc,CAAC,MAAM,GAClC,CAAC,aAAa,YAAY,GAAG,IAAI,CAAC;IAEtC,qBACI;;YAEK,wBACG,8OAAC;gBACG,WAAU;gBACV,SAAS;;;;;;0BAKjB,8OAAC;gBACG,WAAW,CAAC;;;QAGpB,EAAE,SAAS,kBAAkB,qCAAqC;;QAElE,EAAE,UAAU;MACd,CAAC;;kCAGS,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;;kDACX,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAG,WAAU;kDAAyD;;;;;;oCAGtE,qBAAqB,mBAClB,8OAAC;wCAAK,WAAU;kDACX;;;;;;;;;;;;0CAIb,8OAAC;gCACG,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKrB,8OAAC;wBAAI,WAAU;;0CAEX,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAG,WAAU;0DAA+C;;;;;;;;;;;;kDAIjE,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;;kEACG,8OAAC;wDAAM,WAAU;kEAAsD;;;;;;kEAGvE,8OAAC;wDACG,MAAK;wDACL,OACI,aAAa,SAAS,CAAC,KAAK,GACtB,aAAa,SAAS,CAAC,KAAK,CACvB,WAAW,GACX,KAAK,CAAC,IAAI,CAAC,EAAE,GAClB;wDAEV,UAAU,CAAC,IACP,sBACI,SACA,EAAE,MAAM,CAAC,KAAK;wDAGtB,WAAU;;;;;;;;;;;;0DAGlB,8OAAC;;kEACG,8OAAC;wDAAM,WAAU;kEAAsD;;;;;;kEAGvE,8OAAC;wDACG,MAAK;wDACL,OACI,aAAa,SAAS,CAAC,GAAG,GACpB,aAAa,SAAS,CAAC,GAAG,CACrB,WAAW,GACX,KAAK,CAAC,IAAI,CAAC,EAAE,GAClB;wDAEV,UAAU,CAAC,IACP,sBACI,OACA,EAAE,MAAM,CAAC,KAAK;wDAGtB,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAO1B,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAG,WAAU;0DAA+C;;;;;;;;;;;;kDAIjE,8OAAC;wCAAI,WAAU;kDACV,iBAAiB,GAAG,CAAC,CAAC,sBACnB,8OAAC;gDAEG,WAAU;;kEAEV,8OAAC;wDACG,MAAK;wDACL,SAAS,aAAa,gBAAgB,CAAC,QAAQ,CAC3C;wDAEJ,UAAU,IACN,wBACI,oBACA;wDAGR,WAAU;;;;;;kEAEd,8OAAC;wDAAK,WAAU;kEACX;;;;;;;+CAjBA;;;;;;;;;;;;;;;;0CAyBrB,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAG,WAAU;0DAA+C;;;;;;;;;;;;kDAIjE,8OAAC;wCAAI,WAAU;kDACV,qHAAA,CAAA,uBAAoB,CAAC,GAAG,CAAC,CAAC,yBACvB,8OAAC;gDAEG,WAAU;;kEAEV,8OAAC;wDACG,MAAK;wDACL,SAAS,aAAa,SAAS,CAAC,QAAQ,CACpC;wDAEJ,UAAU,IACN,wBACI,aACA;wDAGR,WAAU;;;;;;kEAEd,8OAAC;wDAAK,WAAU;kEACX;;;;;;;+CAjBA;;;;;;;;;;;;;;;;0CAyBrB,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;gDAAG,WAAU;0DAA+C;;;;;;;;;;;;kDAIjE,8OAAC;wCAAI,WAAU;kDACV,AAAC;4CAAC;4CAAQ;4CAAU;yCAAM,CAAqB,GAAG,CAC/C,CAAC,sBACG,8OAAC;gDAEG,WAAU;;kEAEV,8OAAC;wDACG,MAAK;wDACL,SAAS,aAAa,cAAc,CAAC,QAAQ,CACzC;wDAEJ,UAAU,IACN,wBACI,kBACA;wDAGR,WAAU;;;;;;kEAEd,8OAAC;wDAAK,WAAU;kEACX;;;;;;;+CAjBA;;;;;;;;;;;;;;;;0CA0BzB,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAG,WAAU;0DAA+C;;;;;;;;;;;;kDAIjE,8OAAC;wCAAM,WAAU;;0DACb,8OAAC;gDACG,MAAK;gDACL,SAAS,aAAa,YAAY;gDAClC,UAAU,CAAC,IACP,gBAAgB,CAAC,OAAS,CAAC;4DACvB,GAAG,IAAI;4DACP,cAAc,EAAE,MAAM,CAAC,OAAO;wDAClC,CAAC;gDAEL,WAAU;;;;;;0DAEd,8OAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;;;;;;;kCAQvE,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCACG,SAAS;gCACT,WAAU;0CACb;;;;;;0CAGD,8OAAC;gCACG,SAAS;gCACT,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/components/EventPopup.tsx"], "sourcesContent": ["'use client';\n\nimport { ViolenceEvent, SeverityLevel } from '@/types';\nimport { format } from 'date-fns';\nimport { \n  Calendar, \n  MapPin, \n  Users, \n  AlertTriangle, \n  CheckCircle, \n  XCircle,\n  ExternalLink,\n  Heart,\n  Skull\n} from 'lucide-react';\nimport Image from 'next/image';\n\ninterface EventPopupProps {\n  event: ViolenceEvent;\n  showFullDetails?: boolean;\n}\n\n// Severity level styling\nconst SEVERITY_STYLES: Record<SeverityLevel, { bg: string; text: string; border: string }> = {\n  high: { \n    bg: 'bg-red-100 dark:bg-red-900/30', \n    text: 'text-red-800 dark:text-red-200', \n    border: 'border-red-200 dark:border-red-700' \n  },\n  medium: { \n    bg: 'bg-orange-100 dark:bg-orange-900/30', \n    text: 'text-orange-800 dark:text-orange-200', \n    border: 'border-orange-200 dark:border-orange-700' \n  },\n  low: { \n    bg: 'bg-yellow-100 dark:bg-yellow-900/30', \n    text: 'text-yellow-800 dark:text-yellow-200', \n    border: 'border-yellow-200 dark:border-yellow-700' \n  },\n};\n\nexport default function EventPopup({ event, showFullDetails = false }: EventPopupProps) {\n  const severityStyle = SEVERITY_STYLES[event.severity];\n  const eventDate = new Date(event.date);\n  const totalCasualties = event.casualties.injured + event.casualties.dead;\n\n  return (\n    <div className=\"space-y-3 text-sm\">\n      {/* Header */}\n      <div className=\"space-y-2\">\n        <h3 className=\"font-semibold text-gray-900 dark:text-gray-100 leading-tight\">\n          {event.title}\n        </h3>\n        \n        {/* Severity badge */}\n        <div className=\"flex items-center justify-between\">\n          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${severityStyle.bg} ${severityStyle.text} ${severityStyle.border}`}>\n            <AlertTriangle className=\"w-3 h-3 mr-1\" />\n            {event.severity.toUpperCase()}\n          </span>\n          \n          {/* Verification status */}\n          <div className=\"flex items-center space-x-1\">\n            {event.verified ? (\n              <CheckCircle className=\"w-4 h-4 text-green-500\" />\n            ) : (\n              <XCircle className=\"w-4 h-4 text-gray-400\" />\n            )}\n            <span className={`text-xs ${event.verified ? 'text-green-600 dark:text-green-400' : 'text-gray-500'}`}>\n              {event.verified ? 'Verified' : 'Unverified'}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Event image */}\n      {event.imageUrl && (\n        <div className=\"relative w-full h-32 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800\">\n          <Image\n            src={event.imageUrl}\n            alt={event.title}\n            fill\n            className=\"object-cover\"\n            sizes=\"(max-width: 320px) 100vw, 320px\"\n          />\n        </div>\n      )}\n\n      {/* Summary */}\n      <p className=\"text-gray-700 dark:text-gray-300 leading-relaxed\">\n        {event.summary}\n      </p>\n\n      {/* Key details */}\n      <div className=\"space-y-2\">\n        {/* Date and time */}\n        <div className=\"flex items-center space-x-2 text-gray-600 dark:text-gray-400\">\n          <Calendar className=\"w-4 h-4\" />\n          <span>{format(eventDate, 'PPP')}</span>\n          <span className=\"text-xs\">({format(eventDate, 'p')})</span>\n        </div>\n\n        {/* Location */}\n        <div className=\"flex items-center space-x-2 text-gray-600 dark:text-gray-400\">\n          <MapPin className=\"w-4 h-4\" />\n          <span>{event.location.address}</span>\n        </div>\n\n        {/* Political parties involved */}\n        <div className=\"flex items-center space-x-2 text-gray-600 dark:text-gray-400\">\n          <Users className=\"w-4 h-4\" />\n          <span className=\"text-xs\">\n            <span className=\"font-medium\">{event.politicalParty}</span>\n            {event.opposingParty && (\n              <>\n                <span className=\"mx-1\">vs</span>\n                <span className=\"font-medium\">{event.opposingParty}</span>\n              </>\n            )}\n          </span>\n        </div>\n      </div>\n\n      {/* Casualties */}\n      {totalCasualties > 0 && (\n        <div className=\"bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 space-y-2\">\n          <h4 className=\"font-medium text-gray-900 dark:text-gray-100 text-xs uppercase tracking-wide\">\n            Casualties\n          </h4>\n          <div className=\"flex items-center justify-between text-sm\">\n            {event.casualties.injured > 0 && (\n              <div className=\"flex items-center space-x-1 text-orange-600 dark:text-orange-400\">\n                <Heart className=\"w-4 h-4\" />\n                <span>{event.casualties.injured} injured</span>\n              </div>\n            )}\n            {event.casualties.dead > 0 && (\n              <div className=\"flex items-center space-x-1 text-red-600 dark:text-red-400\">\n                <Skull className=\"w-4 h-4\" />\n                <span>{event.casualties.dead} dead</span>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Source and additional details */}\n      <div className=\"pt-2 border-t border-gray-200 dark:border-gray-700\">\n        <div className=\"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400\">\n          <span>Source: {event.source}</span>\n          <button className=\"flex items-center space-x-1 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\">\n            <ExternalLink className=\"w-3 h-3\" />\n            <span>Details</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Administrative info */}\n      {showFullDetails && (\n        <div className=\"pt-2 border-t border-gray-200 dark:border-gray-700 space-y-1\">\n          <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n            <span className=\"font-medium\">Division:</span> {event.location.division}\n          </div>\n          <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n            <span className=\"font-medium\">District:</span> {event.location.district}\n          </div>\n          <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n            <span className=\"font-medium\">Event ID:</span> {event.id}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAfA;;;;;AAsBA,yBAAyB;AACzB,MAAM,kBAAuF;IAC3F,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,QAAQ;IACV;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,QAAQ;IACV;IACA,KAAK;QACH,IAAI;QACJ,MAAM;QACN,QAAQ;IACV;AACF;AAEe,SAAS,WAAW,EAAE,KAAK,EAAE,kBAAkB,KAAK,EAAmB;IACpF,MAAM,gBAAgB,eAAe,CAAC,MAAM,QAAQ,CAAC;IACrD,MAAM,YAAY,IAAI,KAAK,MAAM,IAAI;IACrC,MAAM,kBAAkB,MAAM,UAAU,CAAC,OAAO,GAAG,MAAM,UAAU,CAAC,IAAI;IAExE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX,MAAM,KAAK;;;;;;kCAId,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAW,CAAC,2EAA2E,EAAE,cAAc,EAAE,CAAC,CAAC,EAAE,cAAc,IAAI,CAAC,CAAC,EAAE,cAAc,MAAM,EAAE;;kDAC7J,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCACxB,MAAM,QAAQ,CAAC,WAAW;;;;;;;0CAI7B,8OAAC;gCAAI,WAAU;;oCACZ,MAAM,QAAQ,iBACb,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;6DAEvB,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDAErB,8OAAC;wCAAK,WAAW,CAAC,QAAQ,EAAE,MAAM,QAAQ,GAAG,uCAAuC,iBAAiB;kDAClG,MAAM,QAAQ,GAAG,aAAa;;;;;;;;;;;;;;;;;;;;;;;;YAOtC,MAAM,QAAQ,kBACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAK,MAAM,QAAQ;oBACnB,KAAK,MAAM,KAAK;oBAChB,IAAI;oBACJ,WAAU;oBACV,OAAM;;;;;;;;;;;0BAMZ,8OAAC;gBAAE,WAAU;0BACV,MAAM,OAAO;;;;;;0BAIhB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;0CAAM,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;;;;;;0CACzB,8OAAC;gCAAK,WAAU;;oCAAU;oCAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;oCAAK;;;;;;;;;;;;;kCAIrD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;0CAAM,MAAM,QAAQ,CAAC,OAAO;;;;;;;;;;;;kCAI/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;;kDACd,8OAAC;wCAAK,WAAU;kDAAe,MAAM,cAAc;;;;;;oCAClD,MAAM,aAAa,kBAClB;;0DACE,8OAAC;gDAAK,WAAU;0DAAO;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DAAe,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ3D,kBAAkB,mBACjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA+E;;;;;;kCAG7F,8OAAC;wBAAI,WAAU;;4BACZ,MAAM,UAAU,CAAC,OAAO,GAAG,mBAC1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;;4CAAM,MAAM,UAAU,CAAC,OAAO;4CAAC;;;;;;;;;;;;;4BAGnC,MAAM,UAAU,CAAC,IAAI,GAAG,mBACvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;;4CAAM,MAAM,UAAU,CAAC,IAAI;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;gCAAK;gCAAS,MAAM,MAAM;;;;;;;sCAC3B,8OAAC;4BAAO,WAAU;;8CAChB,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;YAMX,iCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAgB;4BAAE,MAAM,QAAQ,CAAC,QAAQ;;;;;;;kCAEzE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAgB;4BAAE,MAAM,QAAQ,CAAC,QAAQ;;;;;;;kCAEzE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAgB;4BAAE,MAAM,EAAE;;;;;;;;;;;;;;;;;;;AAMpE", "debugId": null}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/components/EventMarker.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { Marker, Popup } from 'react-leaflet';\nimport L from 'leaflet';\nimport { ViolenceEvent, SeverityLevel } from '@/types';\nimport EventPopup from './EventPopup';\nimport { format } from 'date-fns';\n\ninterface EventMarkerProps {\n  event: ViolenceEvent;\n  isSelected?: boolean;\n  onClick?: () => void;\n}\n\n// Color mapping for severity levels\nconst SEVERITY_COLORS: Record<SeverityLevel, string> = {\n  high: '#ef4444', // red-500\n  medium: '#f97316', // orange-500\n  low: '#eab308', // yellow-500\n};\n\n// Create custom marker icons based on severity and selection state\nfunction createMarkerIcon(severity: SeverityLevel, isSelected: boolean = false, isRecent: boolean = false) {\n  const color = SEVERITY_COLORS[severity];\n  const size = isSelected ? 32 : 24;\n  const pulseClass = isRecent ? 'animate-pulse' : '';\n  \n  // Create SVG icon\n  const svgIcon = `\n    <svg width=\"${size}\" height=\"${size}\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n      <defs>\n        <filter id=\"shadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n          <feDropShadow dx=\"0\" dy=\"2\" stdDeviation=\"2\" flood-color=\"rgba(0,0,0,0.3)\"/>\n        </filter>\n      </defs>\n      <circle \n        cx=\"12\" \n        cy=\"12\" \n        r=\"${isSelected ? '10' : '8'}\" \n        fill=\"${color}\" \n        stroke=\"white\" \n        stroke-width=\"${isSelected ? '3' : '2'}\"\n        filter=\"url(#shadow)\"\n        class=\"${pulseClass}\"\n      />\n      ${isSelected ? '<circle cx=\"12\" cy=\"12\" r=\"4\" fill=\"white\" opacity=\"0.8\"/>' : ''}\n    </svg>\n  `;\n\n  return L.divIcon({\n    html: svgIcon,\n    className: 'custom-marker-icon',\n    iconSize: [size, size],\n    iconAnchor: [size / 2, size / 2],\n    popupAnchor: [0, -size / 2],\n  });\n}\n\n// Check if event is recent (within last 7 days)\nfunction isRecentEvent(dateString: string): boolean {\n  const eventDate = new Date(dateString);\n  const now = new Date();\n  const daysDiff = (now.getTime() - eventDate.getTime()) / (1000 * 60 * 60 * 24);\n  return daysDiff <= 7;\n}\n\nexport default function EventMarker({ event, isSelected = false, onClick }: EventMarkerProps) {\n  const markerRef = useRef<L.Marker | null>(null);\n  const isRecent = isRecentEvent(event.date);\n\n  // Create the appropriate icon based on event properties\n  const icon = createMarkerIcon(event.severity, isSelected, isRecent);\n\n  // Handle marker click\n  const handleClick = () => {\n    onClick?.();\n  };\n\n  // Auto-open popup for selected events\n  useEffect(() => {\n    if (isSelected && markerRef.current) {\n      markerRef.current.openPopup();\n    }\n  }, [isSelected]);\n\n  return (\n    <Marker\n      position={[event.location.lat, event.location.lng]}\n      icon={icon}\n      ref={markerRef}\n      eventHandlers={{\n        click: handleClick,\n      }}\n    >\n      <Popup\n        closeButton={true}\n        autoClose={false}\n        closeOnClick={false}\n        className=\"custom-popup\"\n        maxWidth={320}\n        minWidth={280}\n      >\n        <EventPopup event={event} />\n      </Popup>\n    </Marker>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;AANA;;;;;;AAeA,oCAAoC;AACpC,MAAM,kBAAiD;IACrD,MAAM;IACN,QAAQ;IACR,KAAK;AACP;AAEA,mEAAmE;AACnE,SAAS,iBAAiB,QAAuB,EAAE,aAAsB,KAAK,EAAE,WAAoB,KAAK;IACvG,MAAM,QAAQ,eAAe,CAAC,SAAS;IACvC,MAAM,OAAO,aAAa,KAAK;IAC/B,MAAM,aAAa,WAAW,kBAAkB;IAEhD,kBAAkB;IAClB,MAAM,UAAU,CAAC;gBACH,EAAE,KAAK,UAAU,EAAE,KAAK;;;;;;;;;WAS7B,EAAE,aAAa,OAAO,IAAI;cACvB,EAAE,MAAM;;sBAEA,EAAE,aAAa,MAAM,IAAI;;eAEhC,EAAE,WAAW;;MAEtB,EAAE,aAAa,+DAA+D,GAAG;;EAErF,CAAC;IAED,OAAO,iJAAA,CAAA,UAAC,CAAC,OAAO,CAAC;QACf,MAAM;QACN,WAAW;QACX,UAAU;YAAC;YAAM;SAAK;QACtB,YAAY;YAAC,OAAO;YAAG,OAAO;SAAE;QAChC,aAAa;YAAC;YAAG,CAAC,OAAO;SAAE;IAC7B;AACF;AAEA,gDAAgD;AAChD,SAAS,cAAc,UAAkB;IACvC,MAAM,YAAY,IAAI,KAAK;IAC3B,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,CAAC,IAAI,OAAO,KAAK,UAAU,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAC7E,OAAO,YAAY;AACrB;AAEe,SAAS,YAAY,EAAE,KAAK,EAAE,aAAa,KAAK,EAAE,OAAO,EAAoB;IAC1F,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAmB;IAC1C,MAAM,WAAW,cAAc,MAAM,IAAI;IAEzC,wDAAwD;IACxD,MAAM,OAAO,iBAAiB,MAAM,QAAQ,EAAE,YAAY;IAE1D,sBAAsB;IACtB,MAAM,cAAc;QAClB;IACF;IAEA,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,UAAU,OAAO,EAAE;YACnC,UAAU,OAAO,CAAC,SAAS;QAC7B;IACF,GAAG;QAAC;KAAW;IAEf,qBACE,8OAAC,iJAAA,CAAA,SAAM;QACL,UAAU;YAAC,MAAM,QAAQ,CAAC,GAAG;YAAE,MAAM,QAAQ,CAAC,GAAG;SAAC;QAClD,MAAM;QACN,KAAK;QACL,eAAe;YACb,OAAO;QACT;kBAEA,cAAA,8OAAC,gJAAA,CAAA,QAAK;YACJ,aAAa;YACb,WAAW;YACX,cAAc;YACd,WAAU;YACV,UAAU;YACV,UAAU;sBAEV,cAAA,8OAAC,gIAAA,CAAA,UAAU;gBAAC,OAAO;;;;;;;;;;;;;;;;AAI3B", "debugId": null}}, {"offset": {"line": 1203, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/components/MapView.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, Tile<PERSON>ayer, useMap } from 'react-leaflet';\nimport L from 'leaflet';\nimport 'leaflet/dist/leaflet.css';\nimport { ViolenceEvent, BANGLADESH_CENTER, BANGLADESH_BOUNDS } from '@/types';\nimport EventMarker from './EventMarker';\nimport { Loader2 } from 'lucide-react';\n\n// Fix for default markers in react-leaflet\ndelete (L.Icon.Default.prototype as any)._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\n});\n\ninterface MapViewProps {\n  events: ViolenceEvent[];\n  selectedEvent?: ViolenceEvent | null;\n  onEventSelect?: (event: ViolenceEvent | null) => void;\n  className?: string;\n  loading?: boolean;\n}\n\n// Component to handle map bounds and fit to Bangladesh\nfunction MapController({ events }: { events: ViolenceEvent[] }) {\n  const map = useMap();\n\n  useEffect(() => {\n    if (events.length > 0) {\n      // Create bounds from all event locations\n      const group = new L.FeatureGroup(\n        events.map(event => \n          L.marker([event.location.lat, event.location.lng])\n        )\n      );\n      \n      // Fit map to show all events, with some padding\n      if (group.getBounds().isValid()) {\n        map.fitBounds(group.getBounds(), { padding: [20, 20] });\n      }\n    } else {\n      // Default view of Bangladesh\n      map.setView(BANGLADESH_CENTER, 7);\n    }\n  }, [events, map]);\n\n  return null;\n}\n\nexport default function MapView({ \n  events, \n  selectedEvent, \n  onEventSelect, \n  className = '',\n  loading = false \n}: MapViewProps) {\n  const [mapReady, setMapReady] = useState(false);\n  const mapRef = useRef<L.Map | null>(null);\n\n  // Handle map ready state\n  const handleMapReady = () => {\n    setMapReady(true);\n  };\n\n  // Handle event marker click\n  const handleMarkerClick = (event: ViolenceEvent) => {\n    onEventSelect?.(event);\n  };\n\n  // Handle map click (deselect event)\n  const handleMapClick = () => {\n    onEventSelect?.(null);\n  };\n\n  if (loading) {\n    return (\n      <div className={`relative bg-gray-100 dark:bg-gray-800 rounded-lg ${className}`}>\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"flex items-center space-x-2 text-gray-600 dark:text-gray-300\">\n            <Loader2 className=\"h-6 w-6 animate-spin\" />\n            <span>Loading map...</span>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`relative ${className}`}>\n      <MapContainer\n        center={BANGLADESH_CENTER}\n        zoom={7}\n        className=\"h-full w-full rounded-lg z-0\"\n        whenReady={handleMapReady}\n        ref={mapRef}\n        onClick={handleMapClick}\n        maxBounds={[\n          [BANGLADESH_BOUNDS.south - 1, BANGLADESH_BOUNDS.west - 1],\n          [BANGLADESH_BOUNDS.north + 1, BANGLADESH_BOUNDS.east + 1]\n        ]}\n        minZoom={6}\n        maxZoom={18}\n      >\n        {/* Base tile layer */}\n        <TileLayer\n          attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n          url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n        />\n        \n        {/* Alternative tile layer for better contrast */}\n        {/* <TileLayer\n          attribution='&copy; <a href=\"https://carto.com/attributions\">CARTO</a>'\n          url=\"https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png\"\n        /> */}\n\n        {/* Map controller for bounds management */}\n        <MapController events={events} />\n\n        {/* Event markers */}\n        {mapReady && events.map((event) => (\n          <EventMarker\n            key={event.id}\n            event={event}\n            isSelected={selectedEvent?.id === event.id}\n            onClick={() => handleMarkerClick(event)}\n          />\n        ))}\n      </MapContainer>\n\n      {/* Map overlay info */}\n      <div className=\"absolute top-4 left-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 z-10\">\n        <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\n          Political Violence Map\n        </div>\n        <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n          {events.length} events displayed\n        </div>\n      </div>\n\n      {/* Legend */}\n      <div className=\"absolute bottom-4 left-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 z-10\">\n        <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n          Severity Levels\n        </div>\n        <div className=\"space-y-1\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-3 h-3 rounded-full bg-red-500\"></div>\n            <span className=\"text-xs text-gray-600 dark:text-gray-400\">High</span>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-3 h-3 rounded-full bg-orange-500\"></div>\n            <span className=\"text-xs text-gray-600 dark:text-gray-400\">Medium</span>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-3 h-3 rounded-full bg-yellow-500\"></div>\n            <span className=\"text-xs text-gray-600 dark:text-gray-400\">Low</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Performance indicator for large datasets */}\n      {events.length > 100 && (\n        <div className=\"absolute top-4 right-4 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-lg text-xs z-10\">\n          Large dataset: {events.length} events\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AARA;;;;;;;;;AAUA,2CAA2C;AAC3C,OAAO,AAAC,iJAAA,CAAA,UAAC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAS,WAAW;AACpD,iJAAA,CAAA,UAAC,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;IAC1B,eAAe;IACf,SAAS;IACT,WAAW;AACb;AAUA,uDAAuD;AACvD,SAAS,cAAc,EAAE,MAAM,EAA+B;IAC5D,MAAM,MAAM,CAAA,GAAA,gJAAA,CAAA,SAAM,AAAD;IAEjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,yCAAyC;YACzC,MAAM,QAAQ,IAAI,iJAAA,CAAA,UAAC,CAAC,YAAY,CAC9B,OAAO,GAAG,CAAC,CAAA,QACT,iJAAA,CAAA,UAAC,CAAC,MAAM,CAAC;oBAAC,MAAM,QAAQ,CAAC,GAAG;oBAAE,MAAM,QAAQ,CAAC,GAAG;iBAAC;YAIrD,gDAAgD;YAChD,IAAI,MAAM,SAAS,GAAG,OAAO,IAAI;gBAC/B,IAAI,SAAS,CAAC,MAAM,SAAS,IAAI;oBAAE,SAAS;wBAAC;wBAAI;qBAAG;gBAAC;YACvD;QACF,OAAO;YACL,6BAA6B;YAC7B,IAAI,OAAO,CAAC,qHAAA,CAAA,oBAAiB,EAAE;QACjC;IACF,GAAG;QAAC;QAAQ;KAAI;IAEhB,OAAO;AACT;AAEe,SAAS,QAAQ,EAC9B,MAAM,EACN,aAAa,EACb,aAAa,EACb,YAAY,EAAE,EACd,UAAU,KAAK,EACF;IACb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAgB;IAEpC,yBAAyB;IACzB,MAAM,iBAAiB;QACrB,YAAY;IACd;IAEA,4BAA4B;IAC5B,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,oCAAoC;IACpC,MAAM,iBAAiB;QACrB,gBAAgB;IAClB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAW,CAAC,iDAAiD,EAAE,WAAW;sBAC7E,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;IAKhB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BACrC,8OAAC,uJAAA,CAAA,eAAY;gBACX,QAAQ,qHAAA,CAAA,oBAAiB;gBACzB,MAAM;gBACN,WAAU;gBACV,WAAW;gBACX,KAAK;gBACL,SAAS;gBACT,WAAW;oBACT;wBAAC,qHAAA,CAAA,oBAAiB,CAAC,KAAK,GAAG;wBAAG,qHAAA,CAAA,oBAAiB,CAAC,IAAI,GAAG;qBAAE;oBACzD;wBAAC,qHAAA,CAAA,oBAAiB,CAAC,KAAK,GAAG;wBAAG,qHAAA,CAAA,oBAAiB,CAAC,IAAI,GAAG;qBAAE;iBAC1D;gBACD,SAAS;gBACT,SAAS;;kCAGT,8OAAC,oJAAA,CAAA,YAAS;wBACR,aAAY;wBACZ,KAAI;;;;;;kCAUN,8OAAC;wBAAc,QAAQ;;;;;;oBAGtB,YAAY,OAAO,GAAG,CAAC,CAAC,sBACvB,8OAAC,iIAAA,CAAA,UAAW;4BAEV,OAAO;4BACP,YAAY,eAAe,OAAO,MAAM,EAAE;4BAC1C,SAAS,IAAM,kBAAkB;2BAH5B,MAAM,EAAE;;;;;;;;;;;0BASnB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAuD;;;;;;kCAGtE,8OAAC;wBAAI,WAAU;;4BACZ,OAAO,MAAM;4BAAC;;;;;;;;;;;;;0BAKnB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAA4D;;;;;;kCAG3E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;0CAE7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;0CAE7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;;YAMhE,OAAO,MAAM,GAAG,qBACf,8OAAC;gBAAI,WAAU;;oBAAiI;oBAC9H,OAAO,MAAM;oBAAC;;;;;;;;;;;;;AAKxC", "debugId": null}}, {"offset": {"line": 1516, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport FiltersSidebar from \"@/components/FiltersSidebar\";\nimport MapView from \"@/components/MapView\";\nimport { useApp } from \"@/contexts/AppContext\";\nimport { AlertCircle, Loader2, <PERSON><PERSON>, <PERSON>, <PERSON> } from \"lucide-react\";\n\nexport default function Home() {\n    const { state, actions } = useApp();\n\n    const {\n        filteredEvents,\n        selectedEvent,\n        loading,\n        error,\n        sidebarOpen,\n        darkMode,\n        filters,\n        availableParties,\n        availableDistricts,\n    } = state;\n\n    const {\n        applyFilters,\n        selectEvent,\n        toggleSidebar,\n        toggleDarkMode,\n        setSidebarOpen,\n    } = actions;\n\n    // Handle mobile sidebar close\n    const handleMobileSidebarClose = () => {\n        setSidebarOpen(false);\n    };\n\n    if (error) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n                <div className=\"text-center space-y-4\">\n                    <AlertCircle className=\"w-12 h-12 text-red-500 mx-auto\" />\n                    <h1 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n                        Failed to Load Data\n                    </h1>\n                    <p className=\"text-gray-600 dark:text-gray-400 max-w-md\">\n                        {error}\n                    </p>\n                    <button\n                        onClick={() => window.location.reload()}\n                        className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors\"\n                    >\n                        Retry\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"h-screen bg-gray-50 dark:bg-gray-900 flex overflow-hidden\">\n            {/* Filters Sidebar */}\n            <FiltersSidebar\n                filters={filters}\n                onFiltersChange={applyFilters}\n                availableParties={availableParties}\n                availableDistricts={availableDistricts}\n                isOpen={sidebarOpen}\n                onToggle={handleMobileSidebarClose}\n            />\n\n            {/* Main Content */}\n            <div className=\"flex-1 flex flex-col overflow-hidden\">\n                {/* Header */}\n                <header className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3\">\n                    <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center space-x-4\">\n                            <button\n                                onClick={toggleSidebar}\n                                className=\"p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 lg:hidden\"\n                            >\n                                <Menu className=\"w-5 h-5\" />\n                            </button>\n\n                            <div>\n                                <h1 className=\"text-xl font-bold text-gray-900 dark:text-gray-100\">\n                                    Bangladesh Political Violence Map\n                                </h1>\n                                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                                    {loading ? (\n                                        <span className=\"flex items-center space-x-2\">\n                                            <Loader2 className=\"w-4 h-4 animate-spin\" />\n                                            <span>Loading events...</span>\n                                        </span>\n                                    ) : (\n                                        `${filteredEvents.length} events displayed`\n                                    )}\n                                </p>\n                            </div>\n                        </div>\n\n                        <div className=\"flex items-center space-x-2\">\n                            {/* Dark mode toggle */}\n                            <button\n                                onClick={toggleDarkMode}\n                                className=\"p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                                title={\n                                    darkMode\n                                        ? \"Switch to light mode\"\n                                        : \"Switch to dark mode\"\n                                }\n                            >\n                                {darkMode ? (\n                                    <Sun className=\"w-5 h-5 text-yellow-500\" />\n                                ) : (\n                                    <Moon className=\"w-5 h-5 text-gray-600\" />\n                                )}\n                            </button>\n\n                            {/* Filters toggle for desktop */}\n                            <button\n                                onClick={toggleSidebar}\n                                className=\"hidden lg:flex items-center space-x-2 px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                            >\n                                <Menu className=\"w-4 h-4\" />\n                                <span className=\"text-sm\">Filters</span>\n                            </button>\n                        </div>\n                    </div>\n                </header>\n\n                {/* Map Container */}\n                <main className=\"flex-1 relative\">\n                    <MapView\n                        events={filteredEvents}\n                        selectedEvent={selectedEvent}\n                        onEventSelect={selectEvent}\n                        loading={loading}\n                        className=\"h-full w-full\"\n                    />\n                </main>\n            </div>\n        </div>\n    );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACpB,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,SAAM,AAAD;IAEhC,MAAM,EACF,cAAc,EACd,aAAa,EACb,OAAO,EACP,KAAK,EACL,WAAW,EACX,QAAQ,EACR,OAAO,EACP,gBAAgB,EAChB,kBAAkB,EACrB,GAAG;IAEJ,MAAM,EACF,YAAY,EACZ,WAAW,EACX,aAAa,EACb,cAAc,EACd,cAAc,EACjB,GAAG;IAEJ,8BAA8B;IAC9B,MAAM,2BAA2B;QAC7B,eAAe;IACnB;IAEA,IAAI,OAAO;QACP,qBACI,8OAAC;YAAI,WAAU;sBACX,cAAA,8OAAC;gBAAI,WAAU;;kCACX,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,8OAAC;wBAAE,WAAU;kCACR;;;;;;kCAEL,8OAAC;wBACG,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACb;;;;;;;;;;;;;;;;;IAMjB;IAEA,qBACI,8OAAC;QAAI,WAAU;;0BAEX,8OAAC,oIAAA,CAAA,UAAc;gBACX,SAAS;gBACT,iBAAiB;gBACjB,kBAAkB;gBAClB,oBAAoB;gBACpB,QAAQ;gBACR,UAAU;;;;;;0BAId,8OAAC;gBAAI,WAAU;;kCAEX,8OAAC;wBAAO,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CACG,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGpB,8OAAC;;8DACG,8OAAC;oDAAG,WAAU;8DAAqD;;;;;;8DAGnE,8OAAC;oDAAE,WAAU;8DACR,wBACG,8OAAC;wDAAK,WAAU;;0EACZ,8OAAC,iNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,8OAAC;0EAAK;;;;;;;;;;;+DAGV,GAAG,eAAe,MAAM,CAAC,iBAAiB,CAAC;;;;;;;;;;;;;;;;;;8CAM3D,8OAAC;oCAAI,WAAU;;sDAEX,8OAAC;4CACG,SAAS;4CACT,WAAU;4CACV,OACI,WACM,yBACA;sDAGT,yBACG,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;qEAEf,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAKxB,8OAAC;4CACG,SAAS;4CACT,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO1C,8OAAC;wBAAK,WAAU;kCACZ,cAAA,8OAAC,6HAAA,CAAA,UAAO;4BACJ,QAAQ;4BACR,eAAe;4BACf,eAAe;4BACf,SAAS;4BACT,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAMlC", "debugId": null}}]}