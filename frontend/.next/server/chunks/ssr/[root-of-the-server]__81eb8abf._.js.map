{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/contexts/AppContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AppProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppProvider() from the server but App<PERSON><PERSON>ider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AppContext.tsx <module evaluation>\",\n    \"AppProvider\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/contexts/AppContext.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AppContext.tsx <module evaluation>\",\n    \"default\",\n);\nexport const useApp = registerClientReference(\n    function() { throw new Error(\"Attempted to call useApp() from the server but useApp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AppContext.tsx <module evaluation>\",\n    \"useApp\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6DACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,6DACA", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/contexts/AppContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AppProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppProvider() from the server but App<PERSON>rovider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AppContext.tsx\",\n    \"AppProvider\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/contexts/AppContext.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AppContext.tsx\",\n    \"default\",\n);\nexport const useApp = registerClientReference(\n    function() { throw new Error(\"Attempted to call useApp() from the server but useApp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AppContext.tsx\",\n    \"useApp\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yCACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,yCACA", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/app/layout.tsx"], "sourcesContent": ["import { AppProvider } from \"@/contexts/AppContext\";\nimport type { Metada<PERSON> } from \"next\";\nimport { <PERSON>ei<PERSON>, <PERSON><PERSON>st_Mono } from \"next/font/google\";\nimport \"./globals.css\";\n\nconst geistSans = Geist({\n    variable: \"--font-geist-sans\",\n    subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n    variable: \"--font-geist-mono\",\n    subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n    title: \"Bangladesh Political Violence Map\",\n    description:\n        \"Interactive map tracking political violence events across Bangladesh\",\n};\n\nexport default function RootLayout({\n    children,\n}: Readonly<{\n    children: React.ReactNode;\n}>) {\n    return (\n        <html lang=\"en\">\n            <body\n                className={`${geistSans.variable} ${geistMono.variable} antialiased`}\n            >\n                <AppProvider>{children}</AppProvider>\n            </body>\n        </html>\n    );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;AAeO,MAAM,WAAqB;IAC9B,OAAO;IACP,aACI;AACR;AAEe,SAAS,WAAW,EAC/B,QAAQ,EAGV;IACE,qBACI,8OAAC;QAAK,MAAK;kBACP,cAAA,8OAAC;YACG,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;sBAEpE,cAAA,8OAAC,8HAAA,CAAA,cAAW;0BAAE;;;;;;;;;;;;;;;;AAI9B", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}