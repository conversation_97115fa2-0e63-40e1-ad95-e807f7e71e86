{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/lib/api.ts"], "sourcesContent": ["import { EventsData, ViolenceEvent, FilterOptions } from '@/types';\n\n// Configuration for API endpoints\nconst API_CONFIG = {\n  // For development, use mock data from public folder\n  // In production, this would be your backend API URL\n  baseURL: process.env.NODE_ENV === 'production' \n    ? process.env.NEXT_PUBLIC_API_URL || '/api'\n    : '',\n  endpoints: {\n    events: '/events.json',\n    // Future endpoints for real backend\n    // events: '/api/events',\n    // eventById: '/api/events/:id',\n    // divisions: '/api/divisions',\n    // statistics: '/api/statistics'\n  }\n};\n\nclass ApiService {\n  private baseURL: string;\n\n  constructor() {\n    this.baseURL = API_CONFIG.baseURL;\n  }\n\n  /**\n   * Fetch all violence events\n   * In the future, this will connect to a real backend API\n   */\n  async getEvents(): Promise<EventsData> {\n    try {\n      const response = await fetch(`${this.baseURL}${API_CONFIG.endpoints.events}`);\n      \n      if (!response.ok) {\n        throw new Error(`Failed to fetch events: ${response.statusText}`);\n      }\n\n      const data: EventsData = await response.json();\n      return data;\n    } catch (error) {\n      console.error('Error fetching events:', error);\n      throw new Error('Failed to load violence events data');\n    }\n  }\n\n  /**\n   * Get a single event by ID\n   * Currently filters from all events, but will be a separate API call in the future\n   */\n  async getEventById(id: string): Promise<ViolenceEvent | null> {\n    try {\n      const data = await this.getEvents();\n      const event = data.events.find(event => event.id === id);\n      return event || null;\n    } catch (error) {\n      console.error('Error fetching event by ID:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Filter events based on criteria\n   * Currently done client-side, but will be server-side in the future\n   */\n  async getFilteredEvents(filters: Partial<FilterOptions>): Promise<ViolenceEvent[]> {\n    try {\n      const data = await this.getEvents();\n      let filteredEvents = data.events;\n\n      // Apply date range filter\n      if (filters.dateRange?.start || filters.dateRange?.end) {\n        filteredEvents = filteredEvents.filter(event => {\n          const eventDate = new Date(event.date);\n          const start = filters.dateRange?.start;\n          const end = filters.dateRange?.end;\n          \n          if (start && eventDate < start) return false;\n          if (end && eventDate > end) return false;\n          return true;\n        });\n      }\n\n      // Apply political party filter\n      if (filters.politicalParties && filters.politicalParties.length > 0) {\n        filteredEvents = filteredEvents.filter(event =>\n          filters.politicalParties!.includes(event.politicalParty) ||\n          filters.politicalParties!.includes(event.opposingParty)\n        );\n      }\n\n      // Apply division filter\n      if (filters.divisions && filters.divisions.length > 0) {\n        filteredEvents = filteredEvents.filter(event =>\n          filters.divisions!.includes(event.location.division)\n        );\n      }\n\n      // Apply district filter\n      if (filters.districts && filters.districts.length > 0) {\n        filteredEvents = filteredEvents.filter(event =>\n          filters.districts!.includes(event.location.district)\n        );\n      }\n\n      // Apply severity filter\n      if (filters.severityLevels && filters.severityLevels.length > 0) {\n        filteredEvents = filteredEvents.filter(event =>\n          filters.severityLevels!.includes(event.severity)\n        );\n      }\n\n      // Apply verified only filter\n      if (filters.verifiedOnly) {\n        filteredEvents = filteredEvents.filter(event => event.verified);\n      }\n\n      return filteredEvents;\n    } catch (error) {\n      console.error('Error filtering events:', error);\n      throw new Error('Failed to filter events');\n    }\n  }\n\n  /**\n   * Get events within a geographic bounding box\n   * Useful for map viewport optimization\n   */\n  async getEventsInBounds(bounds: {\n    north: number;\n    south: number;\n    east: number;\n    west: number;\n  }): Promise<ViolenceEvent[]> {\n    try {\n      const data = await this.getEvents();\n      return data.events.filter(event => {\n        const { lat, lng } = event.location;\n        return lat <= bounds.north && \n               lat >= bounds.south && \n               lng <= bounds.east && \n               lng >= bounds.west;\n      });\n    } catch (error) {\n      console.error('Error fetching events in bounds:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Get statistics for dashboard/summary views\n   * Will be a separate API endpoint in the future\n   */\n  async getStatistics() {\n    try {\n      const data = await this.getEvents();\n      const events = data.events;\n\n      const stats = {\n        totalEvents: events.length,\n        totalInjured: events.reduce((sum, event) => sum + event.casualties.injured, 0),\n        totalDeaths: events.reduce((sum, event) => sum + event.casualties.dead, 0),\n        byDivision: {} as Record<string, number>,\n        bySeverity: {} as Record<string, number>,\n        byParty: {} as Record<string, number>,\n        recentEvents: events\n          .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())\n          .slice(0, 5)\n      };\n\n      // Calculate division statistics\n      events.forEach(event => {\n        stats.byDivision[event.location.division] = \n          (stats.byDivision[event.location.division] || 0) + 1;\n      });\n\n      // Calculate severity statistics\n      events.forEach(event => {\n        stats.bySeverity[event.severity] = \n          (stats.bySeverity[event.severity] || 0) + 1;\n      });\n\n      // Calculate party statistics\n      events.forEach(event => {\n        stats.byParty[event.politicalParty] = \n          (stats.byParty[event.politicalParty] || 0) + 1;\n      });\n\n      return stats;\n    } catch (error) {\n      console.error('Error fetching statistics:', error);\n      throw new Error('Failed to load statistics');\n    }\n  }\n}\n\n// Export singleton instance\nexport const apiService = new ApiService();\n\n// Export the class for testing or custom instances\nexport default ApiService;\n"], "names": [], "mappings": ";;;;AAEA,kCAAkC;AAClC,MAAM,aAAa;IACjB,oDAAoD;IACpD,oDAAoD;IACpD,SAAS,sCACL,0BACA;IACJ,WAAW;QACT,QAAQ;IAMV;AACF;AAEA,MAAM;IACI,QAAgB;IAExB,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,WAAW,OAAO;IACnC;IAEA;;;GAGC,GACD,MAAM,YAAiC;QACrC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,WAAW,SAAS,CAAC,MAAM,EAAE;YAE5E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;YAClE;YAEA,MAAM,OAAmB,MAAM,SAAS,IAAI;YAC5C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;;GAGC,GACD,MAAM,aAAa,EAAU,EAAiC;QAC5D,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,CAAC,SAAS;YACjC,MAAM,QAAQ,KAAK,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;YACrD,OAAO,SAAS;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACT;IACF;IAEA;;;GAGC,GACD,MAAM,kBAAkB,OAA+B,EAA4B;QACjF,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,CAAC,SAAS;YACjC,IAAI,iBAAiB,KAAK,MAAM;YAEhC,0BAA0B;YAC1B,IAAI,QAAQ,SAAS,EAAE,SAAS,QAAQ,SAAS,EAAE,KAAK;gBACtD,iBAAiB,eAAe,MAAM,CAAC,CAAA;oBACrC,MAAM,YAAY,IAAI,KAAK,MAAM,IAAI;oBACrC,MAAM,QAAQ,QAAQ,SAAS,EAAE;oBACjC,MAAM,MAAM,QAAQ,SAAS,EAAE;oBAE/B,IAAI,SAAS,YAAY,OAAO,OAAO;oBACvC,IAAI,OAAO,YAAY,KAAK,OAAO;oBACnC,OAAO;gBACT;YACF;YAEA,+BAA+B;YAC/B,IAAI,QAAQ,gBAAgB,IAAI,QAAQ,gBAAgB,CAAC,MAAM,GAAG,GAAG;gBACnE,iBAAiB,eAAe,MAAM,CAAC,CAAA,QACrC,QAAQ,gBAAgB,CAAE,QAAQ,CAAC,MAAM,cAAc,KACvD,QAAQ,gBAAgB,CAAE,QAAQ,CAAC,MAAM,aAAa;YAE1D;YAEA,wBAAwB;YACxB,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;gBACrD,iBAAiB,eAAe,MAAM,CAAC,CAAA,QACrC,QAAQ,SAAS,CAAE,QAAQ,CAAC,MAAM,QAAQ,CAAC,QAAQ;YAEvD;YAEA,wBAAwB;YACxB,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;gBACrD,iBAAiB,eAAe,MAAM,CAAC,CAAA,QACrC,QAAQ,SAAS,CAAE,QAAQ,CAAC,MAAM,QAAQ,CAAC,QAAQ;YAEvD;YAEA,wBAAwB;YACxB,IAAI,QAAQ,cAAc,IAAI,QAAQ,cAAc,CAAC,MAAM,GAAG,GAAG;gBAC/D,iBAAiB,eAAe,MAAM,CAAC,CAAA,QACrC,QAAQ,cAAc,CAAE,QAAQ,CAAC,MAAM,QAAQ;YAEnD;YAEA,6BAA6B;YAC7B,IAAI,QAAQ,YAAY,EAAE;gBACxB,iBAAiB,eAAe,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ;YAChE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;;GAGC,GACD,MAAM,kBAAkB,MAKvB,EAA4B;QAC3B,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,CAAC,SAAS;YACjC,OAAO,KAAK,MAAM,CAAC,MAAM,CAAC,CAAA;gBACxB,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,QAAQ;gBACnC,OAAO,OAAO,OAAO,KAAK,IACnB,OAAO,OAAO,KAAK,IACnB,OAAO,OAAO,IAAI,IAClB,OAAO,OAAO,IAAI;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,EAAE;QACX;IACF;IAEA;;;GAGC,GACD,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,CAAC,SAAS;YACjC,MAAM,SAAS,KAAK,MAAM;YAE1B,MAAM,QAAQ;gBACZ,aAAa,OAAO,MAAM;gBAC1B,cAAc,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,UAAU,CAAC,OAAO,EAAE;gBAC5E,aAAa,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,UAAU,CAAC,IAAI,EAAE;gBACxE,YAAY,CAAC;gBACb,YAAY,CAAC;gBACb,SAAS,CAAC;gBACV,cAAc,OACX,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,IACpE,KAAK,CAAC,GAAG;YACd;YAEA,gCAAgC;YAChC,OAAO,OAAO,CAAC,CAAA;gBACb,MAAM,UAAU,CAAC,MAAM,QAAQ,CAAC,QAAQ,CAAC,GACvC,CAAC,MAAM,UAAU,CAAC,MAAM,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;YACvD;YAEA,gCAAgC;YAChC,OAAO,OAAO,CAAC,CAAA;gBACb,MAAM,UAAU,CAAC,MAAM,QAAQ,CAAC,GAC9B,CAAC,MAAM,UAAU,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI;YAC9C;YAEA,6BAA6B;YAC7B,OAAO,OAAO,CAAC,CAAA;gBACb,MAAM,OAAO,CAAC,MAAM,cAAc,CAAC,GACjC,CAAC,MAAM,OAAO,CAAC,MAAM,cAAc,CAAC,IAAI,CAAC,IAAI;YACjD;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAGO,MAAM,aAAa,IAAI;uCAGf", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/contexts/AppContext.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';\nimport { ViolenceEvent, FilterOptions, EventsData } from '@/types';\nimport { apiService } from '@/lib/api';\n\n// Application state interface\ninterface AppState {\n  // Data\n  events: ViolenceEvent[];\n  filteredEvents: ViolenceEvent[];\n  eventsData: EventsData | null;\n  \n  // UI state\n  selectedEvent: ViolenceEvent | null;\n  loading: boolean;\n  error: string | null;\n  sidebarOpen: boolean;\n  darkMode: boolean;\n  \n  // Filters\n  filters: FilterOptions;\n  \n  // Derived data\n  availableParties: string[];\n  availableDistricts: string[];\n}\n\n// Action types\ntype AppAction =\n  | { type: 'SET_LOADING'; payload: boolean }\n  | { type: 'SET_ERROR'; payload: string | null }\n  | { type: 'SET_EVENTS_DATA'; payload: EventsData }\n  | { type: 'SET_FILTERED_EVENTS'; payload: ViolenceEvent[] }\n  | { type: 'SET_SELECTED_EVENT'; payload: ViolenceEvent | null }\n  | { type: 'SET_SIDEBAR_OPEN'; payload: boolean }\n  | { type: 'SET_DARK_MODE'; payload: boolean }\n  | { type: 'SET_FILTERS'; payload: FilterOptions }\n  | { type: 'TOGGLE_SIDEBAR' }\n  | { type: 'TOGGLE_DARK_MODE' };\n\n// Initial state\nconst initialState: AppState = {\n  events: [],\n  filteredEvents: [],\n  eventsData: null,\n  selectedEvent: null,\n  loading: true,\n  error: null,\n  sidebarOpen: false,\n  darkMode: false,\n  filters: {\n    dateRange: { start: null, end: null },\n    politicalParties: [],\n    divisions: [],\n    districts: [],\n    severityLevels: [],\n    verifiedOnly: false\n  },\n  availableParties: [],\n  availableDistricts: []\n};\n\n// Reducer function\nfunction appReducer(state: AppState, action: AppAction): AppState {\n  switch (action.type) {\n    case 'SET_LOADING':\n      return { ...state, loading: action.payload };\n    \n    case 'SET_ERROR':\n      return { ...state, error: action.payload, loading: false };\n    \n    case 'SET_EVENTS_DATA':\n      const eventsData = action.payload;\n      const availableParties = Array.from(\n        new Set([\n          ...eventsData.events.map(e => e.politicalParty),\n          ...eventsData.events.map(e => e.opposingParty)\n        ])\n      ).filter(Boolean).sort();\n      \n      const availableDistricts = Array.from(\n        new Set(eventsData.events.map(e => e.location.district))\n      ).sort();\n      \n      return {\n        ...state,\n        eventsData,\n        events: eventsData.events,\n        filteredEvents: eventsData.events,\n        availableParties,\n        availableDistricts,\n        loading: false,\n        error: null\n      };\n    \n    case 'SET_FILTERED_EVENTS':\n      return { ...state, filteredEvents: action.payload };\n    \n    case 'SET_SELECTED_EVENT':\n      return { ...state, selectedEvent: action.payload };\n    \n    case 'SET_SIDEBAR_OPEN':\n      return { ...state, sidebarOpen: action.payload };\n    \n    case 'SET_DARK_MODE':\n      return { ...state, darkMode: action.payload };\n    \n    case 'SET_FILTERS':\n      return { ...state, filters: action.payload };\n    \n    case 'TOGGLE_SIDEBAR':\n      return { ...state, sidebarOpen: !state.sidebarOpen };\n    \n    case 'TOGGLE_DARK_MODE':\n      return { ...state, darkMode: !state.darkMode };\n    \n    default:\n      return state;\n  }\n}\n\n// Context\nconst AppContext = createContext<{\n  state: AppState;\n  dispatch: React.Dispatch<AppAction>;\n  actions: {\n    loadEvents: () => Promise<void>;\n    applyFilters: (filters: FilterOptions) => Promise<void>;\n    selectEvent: (event: ViolenceEvent | null) => void;\n    toggleSidebar: () => void;\n    toggleDarkMode: () => void;\n    setSidebarOpen: (open: boolean) => void;\n  };\n} | null>(null);\n\n// Provider component\nexport function AppProvider({ children }: { children: ReactNode }) {\n  const [state, dispatch] = useReducer(appReducer, initialState);\n\n  // Load events from API\n  const loadEvents = async () => {\n    try {\n      dispatch({ type: 'SET_LOADING', payload: true });\n      dispatch({ type: 'SET_ERROR', payload: null });\n      \n      const eventsData = await apiService.getEvents();\n      dispatch({ type: 'SET_EVENTS_DATA', payload: eventsData });\n    } catch (error) {\n      console.error('Failed to load events:', error);\n      dispatch({ \n        type: 'SET_ERROR', \n        payload: error instanceof Error ? error.message : 'Failed to load events' \n      });\n    }\n  };\n\n  // Apply filters and get filtered events\n  const applyFilters = async (filters: FilterOptions) => {\n    try {\n      dispatch({ type: 'SET_FILTERS', payload: filters });\n      \n      const filteredEvents = await apiService.getFilteredEvents(filters);\n      dispatch({ type: 'SET_FILTERED_EVENTS', payload: filteredEvents });\n      \n      // Clear selected event if it's not in filtered results\n      if (state.selectedEvent && !filteredEvents.find(e => e.id === state.selectedEvent!.id)) {\n        dispatch({ type: 'SET_SELECTED_EVENT', payload: null });\n      }\n    } catch (error) {\n      console.error('Failed to apply filters:', error);\n      dispatch({ \n        type: 'SET_ERROR', \n        payload: 'Failed to apply filters' \n      });\n    }\n  };\n\n  // Select an event\n  const selectEvent = (event: ViolenceEvent | null) => {\n    dispatch({ type: 'SET_SELECTED_EVENT', payload: event });\n  };\n\n  // Toggle sidebar\n  const toggleSidebar = () => {\n    dispatch({ type: 'TOGGLE_SIDEBAR' });\n  };\n\n  // Set sidebar open state\n  const setSidebarOpen = (open: boolean) => {\n    dispatch({ type: 'SET_SIDEBAR_OPEN', payload: open });\n  };\n\n  // Toggle dark mode\n  const toggleDarkMode = () => {\n    dispatch({ type: 'TOGGLE_DARK_MODE' });\n    \n    // Persist dark mode preference\n    const newDarkMode = !state.darkMode;\n    localStorage.setItem('darkMode', JSON.stringify(newDarkMode));\n    \n    // Update document class\n    if (newDarkMode) {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n  };\n\n  // Load events on mount\n  useEffect(() => {\n    loadEvents();\n  }, []);\n\n  // Initialize dark mode from localStorage\n  useEffect(() => {\n    const savedDarkMode = localStorage.getItem('darkMode');\n    if (savedDarkMode) {\n      const isDark = JSON.parse(savedDarkMode);\n      dispatch({ type: 'SET_DARK_MODE', payload: isDark });\n      \n      if (isDark) {\n        document.documentElement.classList.add('dark');\n      }\n    }\n  }, []);\n\n  // Actions object\n  const actions = {\n    loadEvents,\n    applyFilters,\n    selectEvent,\n    toggleSidebar,\n    toggleDarkMode,\n    setSidebarOpen\n  };\n\n  return (\n    <AppContext.Provider value={{ state, dispatch, actions }}>\n      {children}\n    </AppContext.Provider>\n  );\n}\n\n// Hook to use the context\nexport function useApp() {\n  const context = useContext(AppContext);\n  if (!context) {\n    throw new Error('useApp must be used within an AppProvider');\n  }\n  return context;\n}\n\nexport default AppContext;\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;AAJA;;;;AAyCA,gBAAgB;AAChB,MAAM,eAAyB;IAC7B,QAAQ,EAAE;IACV,gBAAgB,EAAE;IAClB,YAAY;IACZ,eAAe;IACf,SAAS;IACT,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QACP,WAAW;YAAE,OAAO;YAAM,KAAK;QAAK;QACpC,kBAAkB,EAAE;QACpB,WAAW,EAAE;QACb,WAAW,EAAE;QACb,gBAAgB,EAAE;QAClB,cAAc;IAChB;IACA,kBAAkB,EAAE;IACpB,oBAAoB,EAAE;AACxB;AAEA,mBAAmB;AACnB,SAAS,WAAW,KAAe,EAAE,MAAiB;IACpD,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,SAAS,OAAO,OAAO;YAAC;QAE7C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,OAAO,OAAO,OAAO;gBAAE,SAAS;YAAM;QAE3D,KAAK;YACH,MAAM,aAAa,OAAO,OAAO;YACjC,MAAM,mBAAmB,MAAM,IAAI,CACjC,IAAI,IAAI;mBACH,WAAW,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,cAAc;mBAC3C,WAAW,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,aAAa;aAC9C,GACD,MAAM,CAAC,SAAS,IAAI;YAEtB,MAAM,qBAAqB,MAAM,IAAI,CACnC,IAAI,IAAI,WAAW,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,QAAQ,IACtD,IAAI;YAEN,OAAO;gBACL,GAAG,KAAK;gBACR;gBACA,QAAQ,WAAW,MAAM;gBACzB,gBAAgB,WAAW,MAAM;gBACjC;gBACA;gBACA,SAAS;gBACT,OAAO;YACT;QAEF,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,gBAAgB,OAAO,OAAO;YAAC;QAEpD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,eAAe,OAAO,OAAO;YAAC;QAEnD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,aAAa,OAAO,OAAO;YAAC;QAEjD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,UAAU,OAAO,OAAO;YAAC;QAE9C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,SAAS,OAAO,OAAO;YAAC;QAE7C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,aAAa,CAAC,MAAM,WAAW;YAAC;QAErD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,UAAU,CAAC,MAAM,QAAQ;YAAC;QAE/C;YACE,OAAO;IACX;AACF;AAEA,UAAU;AACV,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAWrB;AAGH,SAAS,YAAY,EAAE,QAAQ,EAA2B;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,YAAY;IAEjD,uBAAuB;IACvB,MAAM,aAAa;QACjB,IAAI;YACF,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAK;YAC9C,SAAS;gBAAE,MAAM;gBAAa,SAAS;YAAK;YAE5C,MAAM,aAAa,MAAM,iHAAA,CAAA,aAAU,CAAC,SAAS;YAC7C,SAAS;gBAAE,MAAM;gBAAmB,SAAS;YAAW;QAC1D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS;gBACP,MAAM;gBACN,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA,wCAAwC;IACxC,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAQ;YAEjD,MAAM,iBAAiB,MAAM,iHAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC;YAC1D,SAAS;gBAAE,MAAM;gBAAuB,SAAS;YAAe;YAEhE,uDAAuD;YACvD,IAAI,MAAM,aAAa,IAAI,CAAC,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,aAAa,CAAE,EAAE,GAAG;gBACtF,SAAS;oBAAE,MAAM;oBAAsB,SAAS;gBAAK;YACvD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;gBACP,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEA,kBAAkB;IAClB,MAAM,cAAc,CAAC;QACnB,SAAS;YAAE,MAAM;YAAsB,SAAS;QAAM;IACxD;IAEA,iBAAiB;IACjB,MAAM,gBAAgB;QACpB,SAAS;YAAE,MAAM;QAAiB;IACpC;IAEA,yBAAyB;IACzB,MAAM,iBAAiB,CAAC;QACtB,SAAS;YAAE,MAAM;YAAoB,SAAS;QAAK;IACrD;IAEA,mBAAmB;IACnB,MAAM,iBAAiB;QACrB,SAAS;YAAE,MAAM;QAAmB;QAEpC,+BAA+B;QAC/B,MAAM,cAAc,CAAC,MAAM,QAAQ;QACnC,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;QAEhD,wBAAwB;QACxB,IAAI,aAAa;YACf,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;QACzC,OAAO;YACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;QAC5C;IACF;IAEA,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,IAAI,eAAe;YACjB,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,SAAS;gBAAE,MAAM;gBAAiB,SAAS;YAAO;YAElD,IAAI,QAAQ;gBACV,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACzC;QACF;IACF,GAAG,EAAE;IAEL,iBAAiB;IACjB,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,WAAW,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAU;QAAQ;kBACpD;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 438, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}