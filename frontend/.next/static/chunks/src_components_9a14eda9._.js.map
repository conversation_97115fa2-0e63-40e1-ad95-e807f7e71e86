{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/components/EventPopup.tsx"], "sourcesContent": ["'use client';\n\nimport { ViolenceEvent, SeverityLevel } from '@/types';\nimport { format } from 'date-fns';\nimport { \n  Calendar, \n  MapPin, \n  Users, \n  AlertTriangle, \n  CheckCircle, \n  XCircle,\n  ExternalLink,\n  Heart,\n  Skull\n} from 'lucide-react';\nimport Image from 'next/image';\n\ninterface EventPopupProps {\n  event: ViolenceEvent;\n  showFullDetails?: boolean;\n}\n\n// Severity level styling\nconst SEVERITY_STYLES: Record<SeverityLevel, { bg: string; text: string; border: string }> = {\n  high: { \n    bg: 'bg-red-100 dark:bg-red-900/30', \n    text: 'text-red-800 dark:text-red-200', \n    border: 'border-red-200 dark:border-red-700' \n  },\n  medium: { \n    bg: 'bg-orange-100 dark:bg-orange-900/30', \n    text: 'text-orange-800 dark:text-orange-200', \n    border: 'border-orange-200 dark:border-orange-700' \n  },\n  low: { \n    bg: 'bg-yellow-100 dark:bg-yellow-900/30', \n    text: 'text-yellow-800 dark:text-yellow-200', \n    border: 'border-yellow-200 dark:border-yellow-700' \n  },\n};\n\nexport default function EventPopup({ event, showFullDetails = false }: EventPopupProps) {\n  const severityStyle = SEVERITY_STYLES[event.severity];\n  const eventDate = new Date(event.date);\n  const totalCasualties = event.casualties.injured + event.casualties.dead;\n\n  return (\n    <div className=\"space-y-3 text-sm\">\n      {/* Header */}\n      <div className=\"space-y-2\">\n        <h3 className=\"font-semibold text-gray-900 dark:text-gray-100 leading-tight\">\n          {event.title}\n        </h3>\n        \n        {/* Severity badge */}\n        <div className=\"flex items-center justify-between\">\n          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${severityStyle.bg} ${severityStyle.text} ${severityStyle.border}`}>\n            <AlertTriangle className=\"w-3 h-3 mr-1\" />\n            {event.severity.toUpperCase()}\n          </span>\n          \n          {/* Verification status */}\n          <div className=\"flex items-center space-x-1\">\n            {event.verified ? (\n              <CheckCircle className=\"w-4 h-4 text-green-500\" />\n            ) : (\n              <XCircle className=\"w-4 h-4 text-gray-400\" />\n            )}\n            <span className={`text-xs ${event.verified ? 'text-green-600 dark:text-green-400' : 'text-gray-500'}`}>\n              {event.verified ? 'Verified' : 'Unverified'}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Event image */}\n      {event.imageUrl && (\n        <div className=\"relative w-full h-32 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800\">\n          <Image\n            src={event.imageUrl}\n            alt={event.title}\n            fill\n            className=\"object-cover\"\n            sizes=\"(max-width: 320px) 100vw, 320px\"\n          />\n        </div>\n      )}\n\n      {/* Summary */}\n      <p className=\"text-gray-700 dark:text-gray-300 leading-relaxed\">\n        {event.summary}\n      </p>\n\n      {/* Key details */}\n      <div className=\"space-y-2\">\n        {/* Date and time */}\n        <div className=\"flex items-center space-x-2 text-gray-600 dark:text-gray-400\">\n          <Calendar className=\"w-4 h-4\" />\n          <span>{format(eventDate, 'PPP')}</span>\n          <span className=\"text-xs\">({format(eventDate, 'p')})</span>\n        </div>\n\n        {/* Location */}\n        <div className=\"flex items-center space-x-2 text-gray-600 dark:text-gray-400\">\n          <MapPin className=\"w-4 h-4\" />\n          <span>{event.location.address}</span>\n        </div>\n\n        {/* Political parties involved */}\n        <div className=\"flex items-center space-x-2 text-gray-600 dark:text-gray-400\">\n          <Users className=\"w-4 h-4\" />\n          <span className=\"text-xs\">\n            <span className=\"font-medium\">{event.politicalParty}</span>\n            {event.opposingParty && (\n              <>\n                <span className=\"mx-1\">vs</span>\n                <span className=\"font-medium\">{event.opposingParty}</span>\n              </>\n            )}\n          </span>\n        </div>\n      </div>\n\n      {/* Casualties */}\n      {totalCasualties > 0 && (\n        <div className=\"bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 space-y-2\">\n          <h4 className=\"font-medium text-gray-900 dark:text-gray-100 text-xs uppercase tracking-wide\">\n            Casualties\n          </h4>\n          <div className=\"flex items-center justify-between text-sm\">\n            {event.casualties.injured > 0 && (\n              <div className=\"flex items-center space-x-1 text-orange-600 dark:text-orange-400\">\n                <Heart className=\"w-4 h-4\" />\n                <span>{event.casualties.injured} injured</span>\n              </div>\n            )}\n            {event.casualties.dead > 0 && (\n              <div className=\"flex items-center space-x-1 text-red-600 dark:text-red-400\">\n                <Skull className=\"w-4 h-4\" />\n                <span>{event.casualties.dead} dead</span>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Source and additional details */}\n      <div className=\"pt-2 border-t border-gray-200 dark:border-gray-700\">\n        <div className=\"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400\">\n          <span>Source: {event.source}</span>\n          <button className=\"flex items-center space-x-1 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\">\n            <ExternalLink className=\"w-3 h-3\" />\n            <span>Details</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Administrative info */}\n      {showFullDetails && (\n        <div className=\"pt-2 border-t border-gray-200 dark:border-gray-700 space-y-1\">\n          <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n            <span className=\"font-medium\">Division:</span> {event.location.division}\n          </div>\n          <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n            <span className=\"font-medium\">District:</span> {event.location.district}\n          </div>\n          <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n            <span className=\"font-medium\">Event ID:</span> {event.id}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAfA;;;;;AAsBA,yBAAyB;AACzB,MAAM,kBAAuF;IAC3F,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,QAAQ;IACV;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,QAAQ;IACV;IACA,KAAK;QACH,IAAI;QACJ,MAAM;QACN,QAAQ;IACV;AACF;AAEe,SAAS,WAAW,KAAmD;QAAnD,EAAE,KAAK,EAAE,kBAAkB,KAAK,EAAmB,GAAnD;IACjC,MAAM,gBAAgB,eAAe,CAAC,MAAM,QAAQ,CAAC;IACrD,MAAM,YAAY,IAAI,KAAK,MAAM,IAAI;IACrC,MAAM,kBAAkB,MAAM,UAAU,CAAC,OAAO,GAAG,MAAM,UAAU,CAAC,IAAI;IAExE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX,MAAM,KAAK;;;;;;kCAId,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAW,AAAC,8EAAiG,OAApB,cAAc,EAAE,EAAC,KAAyB,OAAtB,cAAc,IAAI,EAAC,KAAwB,OAArB,cAAc,MAAM;;kDAC3J,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCACxB,MAAM,QAAQ,CAAC,WAAW;;;;;;;0CAI7B,6LAAC;gCAAI,WAAU;;oCACZ,MAAM,QAAQ,iBACb,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;6DAEvB,6LAAC,+MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDAErB,6LAAC;wCAAK,WAAW,AAAC,WAAkF,OAAxE,MAAM,QAAQ,GAAG,uCAAuC;kDACjF,MAAM,QAAQ,GAAG,aAAa;;;;;;;;;;;;;;;;;;;;;;;;YAOtC,MAAM,QAAQ,kBACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAK,MAAM,QAAQ;oBACnB,KAAK,MAAM,KAAK;oBAChB,IAAI;oBACJ,WAAU;oBACV,OAAM;;;;;;;;;;;0BAMZ,6LAAC;gBAAE,WAAU;0BACV,MAAM,OAAO;;;;;;0BAIhB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;0CAAM,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;;;;;;0CACzB,6LAAC;gCAAK,WAAU;;oCAAU;oCAAE,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;oCAAK;;;;;;;;;;;;;kCAIrD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;0CAAM,MAAM,QAAQ,CAAC,OAAO;;;;;;;;;;;;kCAI/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;wCAAK,WAAU;kDAAe,MAAM,cAAc;;;;;;oCAClD,MAAM,aAAa,kBAClB;;0DACE,6LAAC;gDAAK,WAAU;0DAAO;;;;;;0DACvB,6LAAC;gDAAK,WAAU;0DAAe,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ3D,kBAAkB,mBACjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA+E;;;;;;kCAG7F,6LAAC;wBAAI,WAAU;;4BACZ,MAAM,UAAU,CAAC,OAAO,GAAG,mBAC1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;;4CAAM,MAAM,UAAU,CAAC,OAAO;4CAAC;;;;;;;;;;;;;4BAGnC,MAAM,UAAU,CAAC,IAAI,GAAG,mBACvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;;4CAAM,MAAM,UAAU,CAAC,IAAI;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;gCAAK;gCAAS,MAAM,MAAM;;;;;;;sCAC3B,6LAAC;4BAAO,WAAU;;8CAChB,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;YAMX,iCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAgB;4BAAE,MAAM,QAAQ,CAAC,QAAQ;;;;;;;kCAEzE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAgB;4BAAE,MAAM,QAAQ,CAAC,QAAQ;;;;;;;kCAEzE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAgB;4BAAE,MAAM,EAAE;;;;;;;;;;;;;;;;;;;AAMpE;KApIwB", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/components/EventMarker.tsx"], "sourcesContent": ["\"use client\";\n\nimport { SeverityLevel, ViolenceEvent } from \"@/types\";\nimport { useEffect, useRef } from \"react\";\nimport EventPopup from \"./EventPopup\";\n\n// Check if we're on the client side\nconst isClient = typeof window !== \"undefined\";\n\n// Only import Leaflet on client side\nlet L: any;\nlet Marker: any;\nlet Popup: any;\n\nif (isClient) {\n    L = require(\"leaflet\");\n    const reactLeaflet = require(\"react-leaflet\");\n    Marker = reactLeaflet.Marker;\n    Popup = reactLeaflet.Popup;\n}\n\ninterface EventMarkerProps {\n    event: ViolenceEvent;\n    isSelected?: boolean;\n    onClick?: () => void;\n}\n\n// Color mapping for severity levels\nconst SEVERITY_COLORS: Record<SeverityLevel, string> = {\n    high: \"#ef4444\", // red-500\n    medium: \"#f97316\", // orange-500\n    low: \"#eab308\", // yellow-500\n};\n\n// Create custom marker icons based on severity and selection state\nfunction createMarkerIcon(\n    severity: SeverityLevel,\n    isSelected: boolean = false,\n    isRecent: boolean = false\n) {\n    const color = SEVERITY_COLORS[severity];\n    const size = isSelected ? 32 : 24;\n    const pulseClass = isRecent ? \"animate-pulse\" : \"\";\n\n    // Create SVG icon\n    const svgIcon = `\n    <svg width=\"${size}\" height=\"${size}\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n      <defs>\n        <filter id=\"shadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n          <feDropShadow dx=\"0\" dy=\"2\" stdDeviation=\"2\" flood-color=\"rgba(0,0,0,0.3)\"/>\n        </filter>\n      </defs>\n      <circle \n        cx=\"12\" \n        cy=\"12\" \n        r=\"${isSelected ? \"10\" : \"8\"}\" \n        fill=\"${color}\" \n        stroke=\"white\" \n        stroke-width=\"${isSelected ? \"3\" : \"2\"}\"\n        filter=\"url(#shadow)\"\n        class=\"${pulseClass}\"\n      />\n      ${\n          isSelected\n              ? '<circle cx=\"12\" cy=\"12\" r=\"4\" fill=\"white\" opacity=\"0.8\"/>'\n              : \"\"\n      }\n    </svg>\n  `;\n\n    return L.divIcon({\n        html: svgIcon,\n        className: \"custom-marker-icon\",\n        iconSize: [size, size],\n        iconAnchor: [size / 2, size / 2],\n        popupAnchor: [0, -size / 2],\n    });\n}\n\n// Check if event is recent (within last 7 days)\nfunction isRecentEvent(dateString: string): boolean {\n    const eventDate = new Date(dateString);\n    const now = new Date();\n    const daysDiff =\n        (now.getTime() - eventDate.getTime()) / (1000 * 60 * 60 * 24);\n    return daysDiff <= 7;\n}\n\nexport default function EventMarker({\n    event,\n    isSelected = false,\n    onClick,\n}: EventMarkerProps) {\n    const markerRef = useRef<L.Marker | null>(null);\n    const isRecent = isRecentEvent(event.date);\n\n    // Create the appropriate icon based on event properties\n    const icon = createMarkerIcon(event.severity, isSelected, isRecent);\n\n    // Handle marker click\n    const handleClick = () => {\n        onClick?.();\n    };\n\n    // Auto-open popup for selected events\n    useEffect(() => {\n        if (isSelected && markerRef.current) {\n            markerRef.current.openPopup();\n        }\n    }, [isSelected]);\n\n    // Don't render on server side\n    if (!isClient || !Marker || !Popup) {\n        return null;\n    }\n\n    return (\n        <Marker\n            position={[event.location.lat, event.location.lng]}\n            icon={icon}\n            ref={markerRef}\n            eventHandlers={{\n                click: handleClick,\n            }}\n        >\n            <Popup\n                closeButton={true}\n                autoClose={false}\n                closeOnClick={false}\n                className=\"custom-popup\"\n                maxWidth={320}\n                minWidth={280}\n            >\n                <EventPopup event={event} />\n            </Popup>\n        </Marker>\n    );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAMA,oCAAoC;AACpC,MAAM,WAAW,aAAkB;AAEnC,qCAAqC;AACrC,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,wCAAc;IACV;IACA,MAAM;IACN,SAAS,aAAa,MAAM;IAC5B,QAAQ,aAAa,KAAK;AAC9B;AAQA,oCAAoC;AACpC,MAAM,kBAAiD;IACnD,MAAM;IACN,QAAQ;IACR,KAAK;AACT;AAEA,mEAAmE;AACnE,SAAS,iBACL,QAAuB;QACvB,aAAA,iEAAsB,OACtB,WAAA,iEAAoB;IAEpB,MAAM,QAAQ,eAAe,CAAC,SAAS;IACvC,MAAM,OAAO,aAAa,KAAK;IAC/B,MAAM,aAAa,WAAW,kBAAkB;IAEhD,kBAAkB;IAClB,MAAM,UAAU,AAAC,qBACc,OAAjB,MAAK,cASV,OATsB,MAAK,iVAUxB,OADH,aAAa,OAAO,KAAI,sBAGb,OAFR,OAAM,uDAIL,OAFO,aAAa,MAAM,KAAI,qDAKrC,OAHO,YAAW,uBAMrB,OAHG,aACM,+DACA,IACT;IAIH,OAAO,EAAE,OAAO,CAAC;QACb,MAAM;QACN,WAAW;QACX,UAAU;YAAC;YAAM;SAAK;QACtB,YAAY;YAAC,OAAO;YAAG,OAAO;SAAE;QAChC,aAAa;YAAC;YAAG,CAAC,OAAO;SAAE;IAC/B;AACJ;AAEA,gDAAgD;AAChD,SAAS,cAAc,UAAkB;IACrC,MAAM,YAAY,IAAI,KAAK;IAC3B,MAAM,MAAM,IAAI;IAChB,MAAM,WACF,CAAC,IAAI,OAAO,KAAK,UAAU,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAChE,OAAO,YAAY;AACvB;AAEe,SAAS,YAAY,KAIjB;QAJiB,EAChC,KAAK,EACL,aAAa,KAAK,EAClB,OAAO,EACQ,GAJiB;;IAKhC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAmB;IAC1C,MAAM,WAAW,cAAc,MAAM,IAAI;IAEzC,wDAAwD;IACxD,MAAM,OAAO,iBAAiB,MAAM,QAAQ,EAAE,YAAY;IAE1D,sBAAsB;IACtB,MAAM,cAAc;QAChB,oBAAA,8BAAA;IACJ;IAEA,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,IAAI,cAAc,UAAU,OAAO,EAAE;gBACjC,UAAU,OAAO,CAAC,SAAS;YAC/B;QACJ;gCAAG;QAAC;KAAW;IAEf,8BAA8B;IAC9B,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO;QAChC,OAAO;IACX;IAEA,qBACI,6LAAC;QACG,UAAU;YAAC,MAAM,QAAQ,CAAC,GAAG;YAAE,MAAM,QAAQ,CAAC,GAAG;SAAC;QAClD,MAAM;QACN,KAAK;QACL,eAAe;YACX,OAAO;QACX;kBAEA,cAAA,6LAAC;YACG,aAAa;YACb,WAAW;YACX,cAAc;YACd,WAAU;YACV,UAAU;YACV,UAAU;sBAEV,cAAA,6LAAC,mIAAA,CAAA,UAAU;gBAAC,OAAO;;;;;;;;;;;;;;;;AAInC;GAjDwB;KAAA", "debugId": null}}]}