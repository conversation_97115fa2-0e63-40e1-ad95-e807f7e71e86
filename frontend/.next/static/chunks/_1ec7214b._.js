(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "apiService": ()=>apiService,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
;
// Configuration for API endpoints
const API_CONFIG = {
    // For development, use mock data from public folder
    // In production, this would be your backend API URL
    baseURL: ("TURBOPACK compile-time falsy", 0) ? "TURBOPACK unreachable" : '',
    endpoints: {
        events: '/events.json'
    }
};
class ApiService {
    /**
   * Fetch all violence events
   * In the future, this will connect to a real backend API
   */ async getEvents() {
        try {
            const response = await fetch("".concat(this.baseURL).concat(API_CONFIG.endpoints.events));
            if (!response.ok) {
                throw new Error("Failed to fetch events: ".concat(response.statusText));
            }
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error fetching events:', error);
            throw new Error('Failed to load violence events data');
        }
    }
    /**
   * Get a single event by ID
   * Currently filters from all events, but will be a separate API call in the future
   */ async getEventById(id) {
        try {
            const data = await this.getEvents();
            const event = data.events.find((event)=>event.id === id);
            return event || null;
        } catch (error) {
            console.error('Error fetching event by ID:', error);
            return null;
        }
    }
    /**
   * Filter events based on criteria
   * Currently done client-side, but will be server-side in the future
   */ async getFilteredEvents(filters) {
        try {
            var _filters_dateRange, _filters_dateRange1;
            const data = await this.getEvents();
            let filteredEvents = data.events;
            // Apply date range filter
            if (((_filters_dateRange = filters.dateRange) === null || _filters_dateRange === void 0 ? void 0 : _filters_dateRange.start) || ((_filters_dateRange1 = filters.dateRange) === null || _filters_dateRange1 === void 0 ? void 0 : _filters_dateRange1.end)) {
                filteredEvents = filteredEvents.filter((event)=>{
                    var _filters_dateRange, _filters_dateRange1;
                    const eventDate = new Date(event.date);
                    const start = (_filters_dateRange = filters.dateRange) === null || _filters_dateRange === void 0 ? void 0 : _filters_dateRange.start;
                    const end = (_filters_dateRange1 = filters.dateRange) === null || _filters_dateRange1 === void 0 ? void 0 : _filters_dateRange1.end;
                    if (start && eventDate < start) return false;
                    if (end && eventDate > end) return false;
                    return true;
                });
            }
            // Apply political party filter
            if (filters.politicalParties && filters.politicalParties.length > 0) {
                filteredEvents = filteredEvents.filter((event)=>filters.politicalParties.includes(event.politicalParty) || filters.politicalParties.includes(event.opposingParty));
            }
            // Apply division filter
            if (filters.divisions && filters.divisions.length > 0) {
                filteredEvents = filteredEvents.filter((event)=>filters.divisions.includes(event.location.division));
            }
            // Apply district filter
            if (filters.districts && filters.districts.length > 0) {
                filteredEvents = filteredEvents.filter((event)=>filters.districts.includes(event.location.district));
            }
            // Apply severity filter
            if (filters.severityLevels && filters.severityLevels.length > 0) {
                filteredEvents = filteredEvents.filter((event)=>filters.severityLevels.includes(event.severity));
            }
            // Apply verified only filter
            if (filters.verifiedOnly) {
                filteredEvents = filteredEvents.filter((event)=>event.verified);
            }
            return filteredEvents;
        } catch (error) {
            console.error('Error filtering events:', error);
            throw new Error('Failed to filter events');
        }
    }
    /**
   * Get events within a geographic bounding box
   * Useful for map viewport optimization
   */ async getEventsInBounds(bounds) {
        try {
            const data = await this.getEvents();
            return data.events.filter((event)=>{
                const { lat, lng } = event.location;
                return lat <= bounds.north && lat >= bounds.south && lng <= bounds.east && lng >= bounds.west;
            });
        } catch (error) {
            console.error('Error fetching events in bounds:', error);
            return [];
        }
    }
    /**
   * Get statistics for dashboard/summary views
   * Will be a separate API endpoint in the future
   */ async getStatistics() {
        try {
            const data = await this.getEvents();
            const events = data.events;
            const stats = {
                totalEvents: events.length,
                totalInjured: events.reduce((sum, event)=>sum + event.casualties.injured, 0),
                totalDeaths: events.reduce((sum, event)=>sum + event.casualties.dead, 0),
                byDivision: {},
                bySeverity: {},
                byParty: {},
                recentEvents: events.sort((a, b)=>new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 5)
            };
            // Calculate division statistics
            events.forEach((event)=>{
                stats.byDivision[event.location.division] = (stats.byDivision[event.location.division] || 0) + 1;
            });
            // Calculate severity statistics
            events.forEach((event)=>{
                stats.bySeverity[event.severity] = (stats.bySeverity[event.severity] || 0) + 1;
            });
            // Calculate party statistics
            events.forEach((event)=>{
                stats.byParty[event.politicalParty] = (stats.byParty[event.politicalParty] || 0) + 1;
            });
            return stats;
        } catch (error) {
            console.error('Error fetching statistics:', error);
            throw new Error('Failed to load statistics');
        }
    }
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "baseURL", void 0);
        this.baseURL = API_CONFIG.baseURL;
    }
}
const apiService = new ApiService();
const __TURBOPACK__default__export__ = ApiService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/AppContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AppProvider": ()=>AppProvider,
    "default": ()=>__TURBOPACK__default__export__,
    "useApp": ()=>useApp
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
// Initial state
const initialState = {
    events: [],
    filteredEvents: [],
    eventsData: null,
    selectedEvent: null,
    loading: true,
    error: null,
    sidebarOpen: false,
    darkMode: false,
    filters: {
        dateRange: {
            start: null,
            end: null
        },
        politicalParties: [],
        divisions: [],
        districts: [],
        severityLevels: [],
        verifiedOnly: false
    },
    availableParties: [],
    availableDistricts: []
};
// Reducer function
function appReducer(state, action) {
    switch(action.type){
        case 'SET_LOADING':
            return {
                ...state,
                loading: action.payload
            };
        case 'SET_ERROR':
            return {
                ...state,
                error: action.payload,
                loading: false
            };
        case 'SET_EVENTS_DATA':
            const eventsData = action.payload;
            const availableParties = Array.from(new Set([
                ...eventsData.events.map((e)=>e.politicalParty),
                ...eventsData.events.map((e)=>e.opposingParty)
            ])).filter(Boolean).sort();
            const availableDistricts = Array.from(new Set(eventsData.events.map((e)=>e.location.district))).sort();
            return {
                ...state,
                eventsData,
                events: eventsData.events,
                filteredEvents: eventsData.events,
                availableParties,
                availableDistricts,
                loading: false,
                error: null
            };
        case 'SET_FILTERED_EVENTS':
            return {
                ...state,
                filteredEvents: action.payload
            };
        case 'SET_SELECTED_EVENT':
            return {
                ...state,
                selectedEvent: action.payload
            };
        case 'SET_SIDEBAR_OPEN':
            return {
                ...state,
                sidebarOpen: action.payload
            };
        case 'SET_DARK_MODE':
            return {
                ...state,
                darkMode: action.payload
            };
        case 'SET_FILTERS':
            return {
                ...state,
                filters: action.payload
            };
        case 'TOGGLE_SIDEBAR':
            return {
                ...state,
                sidebarOpen: !state.sidebarOpen
            };
        case 'TOGGLE_DARK_MODE':
            return {
                ...state,
                darkMode: !state.darkMode
            };
        default:
            return state;
    }
}
// Context
const AppContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
function AppProvider(param) {
    let { children } = param;
    _s();
    const [state, dispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useReducer"])(appReducer, initialState);
    // Load events from API
    const loadEvents = async ()=>{
        try {
            dispatch({
                type: 'SET_LOADING',
                payload: true
            });
            dispatch({
                type: 'SET_ERROR',
                payload: null
            });
            const eventsData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiService"].getEvents();
            dispatch({
                type: 'SET_EVENTS_DATA',
                payload: eventsData
            });
        } catch (error) {
            console.error('Failed to load events:', error);
            dispatch({
                type: 'SET_ERROR',
                payload: error instanceof Error ? error.message : 'Failed to load events'
            });
        }
    };
    // Apply filters and get filtered events
    const applyFilters = async (filters)=>{
        try {
            dispatch({
                type: 'SET_FILTERS',
                payload: filters
            });
            const filteredEvents = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiService"].getFilteredEvents(filters);
            dispatch({
                type: 'SET_FILTERED_EVENTS',
                payload: filteredEvents
            });
            // Clear selected event if it's not in filtered results
            if (state.selectedEvent && !filteredEvents.find((e)=>e.id === state.selectedEvent.id)) {
                dispatch({
                    type: 'SET_SELECTED_EVENT',
                    payload: null
                });
            }
        } catch (error) {
            console.error('Failed to apply filters:', error);
            dispatch({
                type: 'SET_ERROR',
                payload: 'Failed to apply filters'
            });
        }
    };
    // Select an event
    const selectEvent = (event)=>{
        dispatch({
            type: 'SET_SELECTED_EVENT',
            payload: event
        });
    };
    // Toggle sidebar
    const toggleSidebar = ()=>{
        dispatch({
            type: 'TOGGLE_SIDEBAR'
        });
    };
    // Set sidebar open state
    const setSidebarOpen = (open)=>{
        dispatch({
            type: 'SET_SIDEBAR_OPEN',
            payload: open
        });
    };
    // Toggle dark mode
    const toggleDarkMode = ()=>{
        dispatch({
            type: 'TOGGLE_DARK_MODE'
        });
        // Persist dark mode preference
        const newDarkMode = !state.darkMode;
        localStorage.setItem('darkMode', JSON.stringify(newDarkMode));
        // Update document class
        if (newDarkMode) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    };
    // Load events on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AppProvider.useEffect": ()=>{
            loadEvents();
        }
    }["AppProvider.useEffect"], []);
    // Initialize dark mode from localStorage
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AppProvider.useEffect": ()=>{
            const savedDarkMode = localStorage.getItem('darkMode');
            if (savedDarkMode) {
                const isDark = JSON.parse(savedDarkMode);
                dispatch({
                    type: 'SET_DARK_MODE',
                    payload: isDark
                });
                if (isDark) {
                    document.documentElement.classList.add('dark');
                }
            }
        }
    }["AppProvider.useEffect"], []);
    // Actions object
    const actions = {
        loadEvents,
        applyFilters,
        selectEvent,
        toggleSidebar,
        toggleDarkMode,
        setSidebarOpen
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AppContext.Provider, {
        value: {
            state,
            dispatch,
            actions
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AppContext.tsx",
        lineNumber: 239,
        columnNumber: 5
    }, this);
}
_s(AppProvider, "GUSXxL/WUElrtHc/X73NyHNRMdw=");
_c = AppProvider;
function useApp() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AppContext);
    if (!context) {
        throw new Error('useApp must be used within an AppProvider');
    }
    return context;
}
_s1(useApp, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
const __TURBOPACK__default__export__ = AppContext;
var _c;
__turbopack_context__.k.register(_c, "AppProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * react-jsx-dev-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function getComponentNameFromType(type) {
        if (null == type) return null;
        if ("function" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE ? null : type.displayName || type.name || null;
        if ("string" === typeof type) return type;
        switch(type){
            case REACT_FRAGMENT_TYPE:
                return "Fragment";
            case REACT_PROFILER_TYPE:
                return "Profiler";
            case REACT_STRICT_MODE_TYPE:
                return "StrictMode";
            case REACT_SUSPENSE_TYPE:
                return "Suspense";
            case REACT_SUSPENSE_LIST_TYPE:
                return "SuspenseList";
            case REACT_ACTIVITY_TYPE:
                return "Activity";
        }
        if ("object" === typeof type) switch("number" === typeof type.tag && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), type.$$typeof){
            case REACT_PORTAL_TYPE:
                return "Portal";
            case REACT_CONTEXT_TYPE:
                return type.displayName || "Context";
            case REACT_CONSUMER_TYPE:
                return (type._context.displayName || "Context") + ".Consumer";
            case REACT_FORWARD_REF_TYPE:
                var innerType = type.render;
                type = type.displayName;
                type || (type = innerType.displayName || innerType.name || "", type = "" !== type ? "ForwardRef(" + type + ")" : "ForwardRef");
                return type;
            case REACT_MEMO_TYPE:
                return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || "Memo";
            case REACT_LAZY_TYPE:
                innerType = type._payload;
                type = type._init;
                try {
                    return getComponentNameFromType(type(innerType));
                } catch (x) {}
        }
        return null;
    }
    function testStringCoercion(value) {
        return "" + value;
    }
    function checkKeyStringCoercion(value) {
        try {
            testStringCoercion(value);
            var JSCompiler_inline_result = !1;
        } catch (e) {
            JSCompiler_inline_result = !0;
        }
        if (JSCompiler_inline_result) {
            JSCompiler_inline_result = console;
            var JSCompiler_temp_const = JSCompiler_inline_result.error;
            var JSCompiler_inline_result$jscomp$0 = "function" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || "Object";
            JSCompiler_temp_const.call(JSCompiler_inline_result, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", JSCompiler_inline_result$jscomp$0);
            return testStringCoercion(value);
        }
    }
    function getTaskName(type) {
        if (type === REACT_FRAGMENT_TYPE) return "<>";
        if ("object" === typeof type && null !== type && type.$$typeof === REACT_LAZY_TYPE) return "<...>";
        try {
            var name = getComponentNameFromType(type);
            return name ? "<" + name + ">" : "<...>";
        } catch (x) {
            return "<...>";
        }
    }
    function getOwner() {
        var dispatcher = ReactSharedInternals.A;
        return null === dispatcher ? null : dispatcher.getOwner();
    }
    function UnknownOwner() {
        return Error("react-stack-top-frame");
    }
    function hasValidKey(config) {
        if (hasOwnProperty.call(config, "key")) {
            var getter = Object.getOwnPropertyDescriptor(config, "key").get;
            if (getter && getter.isReactWarning) return !1;
        }
        return void 0 !== config.key;
    }
    function defineKeyPropWarningGetter(props, displayName) {
        function warnAboutAccessingKey() {
            specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", displayName));
        }
        warnAboutAccessingKey.isReactWarning = !0;
        Object.defineProperty(props, "key", {
            get: warnAboutAccessingKey,
            configurable: !0
        });
    }
    function elementRefGetterWithDeprecationWarning() {
        var componentName = getComponentNameFromType(this.type);
        didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."));
        componentName = this.props.ref;
        return void 0 !== componentName ? componentName : null;
    }
    function ReactElement(type, key, self, source, owner, props, debugStack, debugTask) {
        self = props.ref;
        type = {
            $$typeof: REACT_ELEMENT_TYPE,
            type: type,
            key: key,
            props: props,
            _owner: owner
        };
        null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, "ref", {
            enumerable: !1,
            get: elementRefGetterWithDeprecationWarning
        }) : Object.defineProperty(type, "ref", {
            enumerable: !1,
            value: null
        });
        type._store = {};
        Object.defineProperty(type._store, "validated", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: 0
        });
        Object.defineProperty(type, "_debugInfo", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: null
        });
        Object.defineProperty(type, "_debugStack", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugStack
        });
        Object.defineProperty(type, "_debugTask", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugTask
        });
        Object.freeze && (Object.freeze(type.props), Object.freeze(type));
        return type;
    }
    function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, debugStack, debugTask) {
        var children = config.children;
        if (void 0 !== children) if (isStaticChildren) if (isArrayImpl(children)) {
            for(isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++)validateChildKeys(children[isStaticChildren]);
            Object.freeze && Object.freeze(children);
        } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
        else validateChildKeys(children);
        if (hasOwnProperty.call(config, "key")) {
            children = getComponentNameFromType(type);
            var keys = Object.keys(config).filter(function(k) {
                return "key" !== k;
            });
            isStaticChildren = 0 < keys.length ? "{key: someKey, " + keys.join(": ..., ") + ": ...}" : "{key: someKey}";
            didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? "{" + keys.join(": ..., ") + ": ...}" : "{}", console.error('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);
        }
        children = null;
        void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = "" + maybeKey);
        hasValidKey(config) && (checkKeyStringCoercion(config.key), children = "" + config.key);
        if ("key" in config) {
            maybeKey = {};
            for(var propName in config)"key" !== propName && (maybeKey[propName] = config[propName]);
        } else maybeKey = config;
        children && defineKeyPropWarningGetter(maybeKey, "function" === typeof type ? type.displayName || type.name || "Unknown" : type);
        return ReactElement(type, children, self, source, getOwner(), maybeKey, debugStack, debugTask);
    }
    function validateChildKeys(node) {
        "object" === typeof node && null !== node && node.$$typeof === REACT_ELEMENT_TYPE && node._store && (node._store.validated = 1);
    }
    var React = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), REACT_ELEMENT_TYPE = Symbol.for("react.transitional.element"), REACT_PORTAL_TYPE = Symbol.for("react.portal"), REACT_FRAGMENT_TYPE = Symbol.for("react.fragment"), REACT_STRICT_MODE_TYPE = Symbol.for("react.strict_mode"), REACT_PROFILER_TYPE = Symbol.for("react.profiler"), REACT_CONSUMER_TYPE = Symbol.for("react.consumer"), REACT_CONTEXT_TYPE = Symbol.for("react.context"), REACT_FORWARD_REF_TYPE = Symbol.for("react.forward_ref"), REACT_SUSPENSE_TYPE = Symbol.for("react.suspense"), REACT_SUSPENSE_LIST_TYPE = Symbol.for("react.suspense_list"), REACT_MEMO_TYPE = Symbol.for("react.memo"), REACT_LAZY_TYPE = Symbol.for("react.lazy"), REACT_ACTIVITY_TYPE = Symbol.for("react.activity"), REACT_CLIENT_REFERENCE = Symbol.for("react.client.reference"), ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, hasOwnProperty = Object.prototype.hasOwnProperty, isArrayImpl = Array.isArray, createTask = console.createTask ? console.createTask : function() {
        return null;
    };
    React = {
        react_stack_bottom_frame: function(callStackForError) {
            return callStackForError();
        }
    };
    var specialPropKeyWarningShown;
    var didWarnAboutElementRef = {};
    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(React, UnknownOwner)();
    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));
    var didWarnAboutKeySpread = {};
    exports.Fragment = REACT_FRAGMENT_TYPE;
    exports.jsxDEV = function(type, config, maybeKey, isStaticChildren, source, self) {
        var trackActualOwner = 1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;
        return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, trackActualOwner ? Error("react-stack-top-frame") : unknownOwnerDebugStack, trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask);
    };
}();
}}),
"[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
;
else {
    module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)");
}
}}),
"[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "_": ()=>_define_property
});
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else obj[key] = value;
    return obj;
}
;
}),
}]);

//# sourceMappingURL=_1ec7214b._.js.map