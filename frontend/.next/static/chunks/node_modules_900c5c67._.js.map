{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/%40react-leaflet/core/lib/context.js"], "sourcesContent": ["import { createContext, use } from 'react';\nexport const CONTEXT_VERSION = 1;\nexport function createLeafletContext(map) {\n    return Object.freeze({\n        __version: CONTEXT_VERSION,\n        map\n    });\n}\nexport function extendContext(source, extra) {\n    return Object.freeze({\n        ...source,\n        ...extra\n    });\n}\nexport const LeafletContext = createContext(null);\nexport function useLeafletContext() {\n    const context = use(LeafletContext);\n    if (context == null) {\n        throw new Error('No context provided: useLeafletContext() can only be used in a descendant of <MapContainer>');\n    }\n    return context;\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,MAAM,kBAAkB;AACxB,SAAS,qBAAqB,GAAG;IACpC,OAAO,OAAO,MAAM,CAAC;QACjB,WAAW;QACX;IACJ;AACJ;AACO,SAAS,cAAc,MAAM,EAAE,KAAK;IACvC,OAAO,OAAO,MAAM,CAAC;QACjB,GAAG,MAAM;QACT,GAAG,KAAK;IACZ;AACJ;AACO,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE;AACrC,SAAS;IACZ,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,MAAG,AAAD,EAAE;IACpB,IAAI,WAAW,MAAM;QACjB,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/hooks.js"], "sourcesContent": ["import { useLeafletContext } from '@react-leaflet/core';\nimport { useEffect } from 'react';\nexport function useMap() {\n    return useLeafletContext().map;\n}\nexport function useMapEvent(type, handler) {\n    const map = useMap();\n    useEffect(function addMapEventHandler() {\n        // @ts-ignore event type\n        map.on(type, handler);\n        return function removeMapEventHandler() {\n            // @ts-ignore event type\n            map.off(type, handler);\n        };\n    }, [\n        map,\n        type,\n        handler\n    ]);\n    return map;\n}\nexport function useMapEvents(handlers) {\n    const map = useMap();\n    useEffect(function addMapEventHandlers() {\n        map.on(handlers);\n        return function removeMapEventHandlers() {\n            map.off(handlers);\n        };\n    }, [\n        map,\n        handlers\n    ]);\n    return map;\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AACO,SAAS;IACZ,OAAO,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD,IAAI,GAAG;AAClC;AACO,SAAS,YAAY,IAAI,EAAE,OAAO;IACrC,MAAM,MAAM;IACZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,wBAAwB;QACxB,IAAI,EAAE,CAAC,MAAM;QACb,OAAO,SAAS;YACZ,wBAAwB;YACxB,IAAI,GAAG,CAAC,MAAM;QAClB;IACJ,GAAG;QACC;QACA;QACA;KACH;IACD,OAAO;AACX;AACO,SAAS,aAAa,QAAQ;IACjC,MAAM,MAAM;IACZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,IAAI,EAAE,CAAC;QACP,OAAO,SAAS;YACZ,IAAI,GAAG,CAAC;QACZ;IACJ,GAAG;QACC;QACA;KACH;IACD,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/%40react-leaflet/core/lib/component.js"], "sourcesContent": ["import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';\nimport { createPortal } from 'react-dom';\nimport { LeafletContext } from './context.js';\nexport function createContainerComponent(useElement) {\n    function ContainerComponent(props, forwardedRef) {\n        const { instance, context } = useElement(props).current;\n        useImperativeHandle(forwardedRef, ()=>instance);\n        const { children } = props;\n        return children == null ? null : /*#__PURE__*/ React.createElement(LeafletContext, {\n            value: context\n        }, children);\n    }\n    return /*#__PURE__*/ forwardRef(ContainerComponent);\n}\nexport function createDivOverlayComponent(useElement) {\n    function OverlayComponent(props, forwardedRef) {\n        const [isOpen, setOpen] = useState(false);\n        const { instance } = useElement(props, setOpen).current;\n        useImperativeHandle(forwardedRef, ()=>instance);\n        // biome-ignore lint/correctness/useExhaustiveDependencies: update overlay when children change\n        useEffect(function updateOverlay() {\n            if (isOpen) {\n                instance.update();\n            }\n        }, [\n            instance,\n            isOpen,\n            props.children\n        ]);\n        // @ts-ignore _contentNode missing in type definition\n        const contentNode = instance._contentNode;\n        return contentNode ? /*#__PURE__*/ createPortal(props.children, contentNode) : null;\n    }\n    return /*#__PURE__*/ forwardRef(OverlayComponent);\n}\nexport function createLeafComponent(useElement) {\n    function LeafComponent(props, forwardedRef) {\n        const { instance } = useElement(props).current;\n        useImperativeHandle(forwardedRef, ()=>instance);\n        return null;\n    }\n    return /*#__PURE__*/ forwardRef(LeafComponent);\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AACO,SAAS,yBAAyB,UAAU;IAC/C,SAAS,mBAAmB,KAAK,EAAE,YAAY;QAC3C,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,WAAW,OAAO,OAAO;QACvD,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;+EAAc,IAAI;;QACtC,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,OAAO,YAAY,OAAO,OAAO,WAAW,GAAG,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,+JAAA,CAAA,iBAAc,EAAE;YAC/E,OAAO;QACX,GAAG;IACP;IACA,OAAO,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;AACpC;AACO,SAAS,0BAA0B,UAAU;IAChD,SAAS,iBAAiB,KAAK,EAAE,YAAY;QACzC,MAAM,CAAC,QAAQ,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAW,OAAO,SAAS,OAAO;QACvD,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;8EAAc,IAAI;;QACtC,+FAA+F;QAC/F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;YACf,IAAI,QAAQ;gBACR,SAAS,MAAM;YACnB;QACJ,GAAG;YACC;YACA;YACA,MAAM,QAAQ;SACjB;QACD,qDAAqD;QACrD,MAAM,cAAc,SAAS,YAAY;QACzC,OAAO,cAAc,WAAW,GAAG,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,MAAM,QAAQ,EAAE,eAAe;IACnF;IACA,OAAO,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;AACpC;AACO,SAAS,oBAAoB,UAAU;IAC1C,SAAS,cAAc,KAAK,EAAE,YAAY;QACtC,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAW,OAAO,OAAO;QAC9C,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;qEAAc,IAAI;;QACtC,OAAO;IACX;IACA,OAAO,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/%40react-leaflet/core/lib/control.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nimport { useLeafletContext } from './context.js';\nexport function createControlHook(useElement) {\n    return function useLeafletControl(props) {\n        const context = useLeafletContext();\n        const elementRef = useElement(props, context);\n        const { instance } = elementRef.current;\n        const positionRef = useRef(props.position);\n        const { position } = props;\n        useEffect(function addControl() {\n            instance.addTo(context.map);\n            return function removeControl() {\n                instance.remove();\n            };\n        }, [\n            context.map,\n            instance\n        ]);\n        useEffect(function updateControl() {\n            if (position != null && position !== positionRef.current) {\n                instance.setPosition(position);\n                positionRef.current = position;\n            }\n        }, [\n            instance,\n            position\n        ]);\n        return elementRef;\n    };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,SAAS,kBAAkB,UAAU;IACxC,OAAO,SAAS,kBAAkB,KAAK;QACnC,MAAM,UAAU,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD;QAChC,MAAM,aAAa,WAAW,OAAO;QACrC,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAW,OAAO;QACvC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,MAAM,QAAQ;QACzC,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;YACf,SAAS,KAAK,CAAC,QAAQ,GAAG;YAC1B,OAAO,SAAS;gBACZ,SAAS,MAAM;YACnB;QACJ,GAAG;YACC,QAAQ,GAAG;YACX;SACH;QACD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;YACf,IAAI,YAAY,QAAQ,aAAa,YAAY,OAAO,EAAE;gBACtD,SAAS,WAAW,CAAC;gBACrB,YAAY,OAAO,GAAG;YAC1B;QACJ,GAAG;YACC;YACA;SACH;QACD,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/%40react-leaflet/core/lib/attribution.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nexport function useAttribution(map, attribution) {\n    const attributionRef = useRef(attribution);\n    useEffect(function updateAttribution() {\n        if (attribution !== attributionRef.current && map.attributionControl != null) {\n            if (attributionRef.current != null) {\n                map.attributionControl.removeAttribution(attributionRef.current);\n            }\n            if (attribution != null) {\n                map.attributionControl.addAttribution(attribution);\n            }\n        }\n        attributionRef.current = attribution;\n    }, [\n        map,\n        attribution\n    ]);\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,eAAe,GAAG,EAAE,WAAW;IAC3C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,IAAI,gBAAgB,eAAe,OAAO,IAAI,IAAI,kBAAkB,IAAI,MAAM;YAC1E,IAAI,eAAe,OAAO,IAAI,MAAM;gBAChC,IAAI,kBAAkB,CAAC,iBAAiB,CAAC,eAAe,OAAO;YACnE;YACA,IAAI,eAAe,MAAM;gBACrB,IAAI,kBAAkB,CAAC,cAAc,CAAC;YAC1C;QACJ;QACA,eAAe,OAAO,GAAG;IAC7B,GAAG;QACC;QACA;KACH;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/%40react-leaflet/core/lib/events.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nexport function useEventHandlers(element, eventHandlers) {\n    const eventHandlersRef = useRef(undefined);\n    useEffect(function addEventHandlers() {\n        if (eventHandlers != null) {\n            element.instance.on(eventHandlers);\n        }\n        eventHandlersRef.current = eventHandlers;\n        return function removeEventHandlers() {\n            if (eventHandlersRef.current != null) {\n                element.instance.off(eventHandlersRef.current);\n            }\n            eventHandlersRef.current = null;\n        };\n    }, [\n        element,\n        eventHandlers\n    ]);\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,iBAAiB,OAAO,EAAE,aAAa;IACnD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,IAAI,iBAAiB,MAAM;YACvB,QAAQ,QAAQ,CAAC,EAAE,CAAC;QACxB;QACA,iBAAiB,OAAO,GAAG;QAC3B,OAAO,SAAS;YACZ,IAAI,iBAAiB,OAAO,IAAI,MAAM;gBAClC,QAAQ,QAAQ,CAAC,GAAG,CAAC,iBAAiB,OAAO;YACjD;YACA,iBAAiB,OAAO,GAAG;QAC/B;IACJ,GAAG;QACC;QACA;KACH;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/%40react-leaflet/core/lib/pane.js"], "sourcesContent": ["export function withPane(props, context) {\n    const pane = props.pane ?? context.pane;\n    return pane ? {\n        ...props,\n        pane\n    } : props;\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,SAAS,KAAK,EAAE,OAAO;QACtB;IAAb,MAAM,OAAO,CAAA,cAAA,MAAM,IAAI,cAAV,yBAAA,cAAc,QAAQ,IAAI;IACvC,OAAO,OAAO;QACV,GAAG,KAAK;QACR;IACJ,IAAI;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/%40react-leaflet/core/lib/div-overlay.js"], "sourcesContent": ["import { useAttribution } from './attribution.js';\nimport { useLeafletContext } from './context.js';\nimport { useEventHandlers } from './events.js';\nimport { withPane } from './pane.js';\nexport function createDivOverlayHook(useElement, useLifecycle) {\n    return function useDivOverlay(props, setOpen) {\n        const context = useLeafletContext();\n        const elementRef = useElement(withPane(props, context), context);\n        useAttribution(context.map, props.attribution);\n        useEventHandlers(elementRef.current, props.eventHandlers);\n        useLifecycle(elementRef.current, context, props, setOpen);\n        return elementRef;\n    };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACO,SAAS,qBAAqB,UAAU,EAAE,YAAY;IACzD,OAAO,SAAS,cAAc,KAAK,EAAE,OAAO;QACxC,MAAM,UAAU,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD;QAChC,MAAM,aAAa,WAAW,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,UAAU;QACxD,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,GAAG,EAAE,MAAM,WAAW;QAC7C,CAAA,GAAA,8JAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,OAAO,EAAE,MAAM,aAAa;QACxD,aAAa,WAAW,OAAO,EAAE,SAAS,OAAO;QACjD,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/%40react-leaflet/core/lib/element.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nexport function createElementObject(instance, context, container) {\n    return Object.freeze({\n        instance,\n        context,\n        container\n    });\n}\nexport function createElementHook(createElement, updateElement) {\n    if (updateElement == null) {\n        return function useImmutableLeafletElement(props, context) {\n            const elementRef = useRef(undefined);\n            if (!elementRef.current) elementRef.current = createElement(props, context);\n            return elementRef;\n        };\n    }\n    return function useMutableLeafletElement(props, context) {\n        const elementRef = useRef(undefined);\n        if (!elementRef.current) elementRef.current = createElement(props, context);\n        const propsRef = useRef(props);\n        const { instance } = elementRef.current;\n        useEffect(function updateElementProps() {\n            if (propsRef.current !== props) {\n                updateElement(instance, props, propsRef.current);\n                propsRef.current = props;\n            }\n        }, [\n            instance,\n            props,\n            updateElement\n        ]);\n        return elementRef;\n    };\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACO,SAAS,oBAAoB,QAAQ,EAAE,OAAO,EAAE,SAAS;IAC5D,OAAO,OAAO,MAAM,CAAC;QACjB;QACA;QACA;IACJ;AACJ;AACO,SAAS,kBAAkB,aAAa,EAAE,aAAa;IAC1D,IAAI,iBAAiB,MAAM;QACvB,OAAO,SAAS,2BAA2B,KAAK,EAAE,OAAO;YACrD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;YAC1B,IAAI,CAAC,WAAW,OAAO,EAAE,WAAW,OAAO,GAAG,cAAc,OAAO;YACnE,OAAO;QACX;IACJ;IACA,OAAO,SAAS,yBAAyB,KAAK,EAAE,OAAO;QACnD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;QAC1B,IAAI,CAAC,WAAW,OAAO,EAAE,WAAW,OAAO,GAAG,cAAc,OAAO;QACnE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;QACxB,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAW,OAAO;QACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;YACf,IAAI,SAAS,OAAO,KAAK,OAAO;gBAC5B,cAAc,UAAU,OAAO,SAAS,OAAO;gBAC/C,SAAS,OAAO,GAAG;YACvB;QACJ,GAAG;YACC;YACA;YACA;SACH;QACD,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 354, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/%40react-leaflet/core/lib/layer.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useAttribution } from './attribution.js';\nimport { useLeafletContext } from './context.js';\nimport { useEventHandlers } from './events.js';\nimport { withPane } from './pane.js';\nexport function useLayerLifecycle(element, context) {\n    useEffect(function addLayer() {\n        const container = context.layerContainer ?? context.map;\n        container.addLayer(element.instance);\n        return function removeLayer() {\n            context.layerContainer?.removeLayer(element.instance);\n            context.map.removeLayer(element.instance);\n        };\n    }, [\n        context,\n        element\n    ]);\n}\nexport function createLayerHook(useElement) {\n    return function useLayer(props) {\n        const context = useLeafletContext();\n        const elementRef = useElement(withPane(props, context), context);\n        useAttribution(context.map, props.attribution);\n        useEventHandlers(elementRef.current, props.eventHandlers);\n        useLayerLifecycle(elementRef.current, context);\n        return elementRef;\n    };\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACO,SAAS,kBAAkB,OAAO,EAAE,OAAO;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;YACG;QAAlB,MAAM,YAAY,CAAA,0BAAA,QAAQ,cAAc,cAAtB,qCAAA,0BAA0B,QAAQ,GAAG;QACvD,UAAU,QAAQ,CAAC,QAAQ,QAAQ;QACnC,OAAO,SAAS;gBACZ;aAAA,0BAAA,QAAQ,cAAc,cAAtB,8CAAA,wBAAwB,WAAW,CAAC,QAAQ,QAAQ;YACpD,QAAQ,GAAG,CAAC,WAAW,CAAC,QAAQ,QAAQ;QAC5C;IACJ,GAAG;QACC;QACA;KACH;AACL;AACO,SAAS,gBAAgB,UAAU;IACtC,OAAO,SAAS,SAAS,KAAK;QAC1B,MAAM,UAAU,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD;QAChC,MAAM,aAAa,WAAW,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,UAAU;QACxD,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,GAAG,EAAE,MAAM,WAAW;QAC7C,CAAA,GAAA,8JAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,OAAO,EAAE,MAAM,aAAa;QACxD,kBAAkB,WAAW,OAAO,EAAE;QACtC,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/%40react-leaflet/core/lib/path.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nimport { useLeafletContext } from './context.js';\nimport { useEventHandlers } from './events.js';\nimport { useLayerLifecycle } from './layer.js';\nimport { withPane } from './pane.js';\nexport function usePathOptions(element, props) {\n    const optionsRef = useRef(undefined);\n    useEffect(function updatePathOptions() {\n        if (props.pathOptions !== optionsRef.current) {\n            const options = props.pathOptions ?? {};\n            element.instance.setStyle(options);\n            optionsRef.current = options;\n        }\n    }, [\n        element,\n        props\n    ]);\n}\nexport function createPathHook(useElement) {\n    return function usePath(props) {\n        const context = useLeafletContext();\n        const elementRef = useElement(withPane(props, context), context);\n        useEventHandlers(elementRef.current, props.eventHandlers);\n        useLayerLifecycle(elementRef.current, context);\n        usePathOptions(elementRef.current, props);\n        return elementRef;\n    };\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACO,SAAS,eAAe,OAAO,EAAE,KAAK;IACzC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,IAAI,MAAM,WAAW,KAAK,WAAW,OAAO,EAAE;gBAC1B;YAAhB,MAAM,UAAU,CAAA,qBAAA,MAAM,WAAW,cAAjB,gCAAA,qBAAqB,CAAC;YACtC,QAAQ,QAAQ,CAAC,QAAQ,CAAC;YAC1B,WAAW,OAAO,GAAG;QACzB;IACJ,GAAG;QACC;QACA;KACH;AACL;AACO,SAAS,eAAe,UAAU;IACrC,OAAO,SAAS,QAAQ,KAAK;QACzB,MAAM,UAAU,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD;QAChC,MAAM,aAAa,WAAW,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,UAAU;QACxD,CAAA,GAAA,8JAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,OAAO,EAAE,MAAM,aAAa;QACxD,CAAA,GAAA,6JAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,OAAO,EAAE;QACtC,eAAe,WAAW,OAAO,EAAE;QACnC,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/%40react-leaflet/core/lib/generic.js"], "sourcesContent": ["import { createContainerComponent, createDivOverlayComponent, createLeafComponent } from './component.js';\nimport { createControlHook } from './control.js';\nimport { createDivOverlayHook } from './div-overlay.js';\nimport { createElementHook, createElementObject } from './element.js';\nimport { createLayerHook } from './layer.js';\nimport { createPathHook } from './path.js';\nexport function createControlComponent(createInstance) {\n    function createElement(props, context) {\n        return createElementObject(createInstance(props), context);\n    }\n    const useElement = createElementHook(createElement);\n    const useControl = createControlHook(useElement);\n    return createLeafComponent(useControl);\n}\nexport function createLayerComponent(createElement, updateElement) {\n    const useElement = createElementHook(createElement, updateElement);\n    const useLayer = createLayerHook(useElement);\n    return createContainerComponent(useLayer);\n}\nexport function createOverlayComponent(createElement, useLifecycle) {\n    const useElement = createElementHook(createElement);\n    const useOverlay = createDivOverlayHook(useElement, useLifecycle);\n    return createDivOverlayComponent(useOverlay);\n}\nexport function createPathComponent(createElement, updateElement) {\n    const useElement = createElementHook(createElement, updateElement);\n    const usePath = createPathHook(useElement);\n    return createContainerComponent(usePath);\n}\nexport function createTileLayerComponent(createElement, updateElement) {\n    const useElement = createElementHook(createElement, updateElement);\n    const useLayer = createLayerHook(useElement);\n    return createLeafComponent(useLayer);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACO,SAAS,uBAAuB,cAAc;IACjD,SAAS,cAAc,KAAK,EAAE,OAAO;QACjC,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,eAAe,QAAQ;IACtD;IACA,MAAM,aAAa,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD,EAAE;IACrC,MAAM,aAAa,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD,EAAE;IACrC,OAAO,CAAA,GAAA,iKAAA,CAAA,sBAAmB,AAAD,EAAE;AAC/B;AACO,SAAS,qBAAqB,aAAa,EAAE,aAAa;IAC7D,MAAM,aAAa,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD,EAAE,eAAe;IACpD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD,EAAE;IACjC,OAAO,CAAA,GAAA,iKAAA,CAAA,2BAAwB,AAAD,EAAE;AACpC;AACO,SAAS,uBAAuB,aAAa,EAAE,YAAY;IAC9D,MAAM,aAAa,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD,EAAE;IACrC,MAAM,aAAa,CAAA,GAAA,sKAAA,CAAA,uBAAoB,AAAD,EAAE,YAAY;IACpD,OAAO,CAAA,GAAA,iKAAA,CAAA,4BAAyB,AAAD,EAAE;AACrC;AACO,SAAS,oBAAoB,aAAa,EAAE,aAAa;IAC5D,MAAM,aAAa,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD,EAAE,eAAe;IACpD,MAAM,UAAU,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE;IAC/B,OAAO,CAAA,GAAA,iKAAA,CAAA,2BAAwB,AAAD,EAAE;AACpC;AACO,SAAS,yBAAyB,aAAa,EAAE,aAAa;IACjE,MAAM,aAAa,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD,EAAE,eAAe;IACpD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD,EAAE;IACjC,OAAO,CAAA,GAAA,iKAAA,CAAA,sBAAmB,AAAD,EAAE;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/AttributionControl.js"], "sourcesContent": ["import { createControlComponent } from '@react-leaflet/core';\nimport { Control } from 'leaflet';\nexport const AttributionControl = createControlComponent(function createAttributionControl(props) {\n    return new Control.Attribution(props);\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,MAAM,qBAAqB,CAAA,GAAA,+JAAA,CAAA,yBAAsB,AAAD,EAAE,SAAS,yBAAyB,KAAK;IAC5F,OAAO,IAAI,oJAAA,CAAA,UAAO,CAAC,WAAW,CAAC;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 504, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/%40react-leaflet/core/lib/circle.js"], "sourcesContent": ["export function updateCircle(layer, props, prevProps) {\n    if (props.center !== prevProps.center) {\n        layer.setLatLng(props.center);\n    }\n    if (props.radius != null && props.radius !== prevProps.radius) {\n        layer.setRadius(props.radius);\n    }\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,aAAa,KAAK,EAAE,KAAK,EAAE,SAAS;IAChD,IAAI,MAAM,MAAM,KAAK,UAAU,MAAM,EAAE;QACnC,MAAM,SAAS,CAAC,MAAM,MAAM;IAChC;IACA,IAAI,MAAM,MAAM,IAAI,QAAQ,MAAM,MAAM,KAAK,UAAU,MAAM,EAAE;QAC3D,MAAM,SAAS,CAAC,MAAM,MAAM;IAChC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 519, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/Circle.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext, updateCircle } from '@react-leaflet/core';\nimport { Circle as LeafletCircle } from 'leaflet';\nexport const Circle = createPathComponent(function createCircle({ center, children: _c, ...options }, ctx) {\n    const circle = new LeafletCircle(center, options);\n    return createElementObject(circle, extendContext(ctx, {\n        overlayContainer: circle\n    }));\n}, updateCircle);\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;;;AACO,MAAM,SAAS,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,aAAa,KAAoC,EAAE,GAAG;QAAzC,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,GAAG,SAAS,GAApC;IAC5D,MAAM,SAAS,IAAI,oJAAA,CAAA,SAAa,CAAC,QAAQ;IACzC,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;QAClD,kBAAkB;IACtB;AACJ,GAAG,8JAAA,CAAA,eAAY", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/CircleMarker.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext, updateCircle } from '@react-leaflet/core';\nimport { CircleMarker as LeafletCircleMarker } from 'leaflet';\nexport const CircleMarker = createPathComponent(function createCircleMarker({ center, children: _c, ...options }, ctx) {\n    const marker = new LeafletCircleMarker(center, options);\n    return createElementObject(marker, extendContext(ctx, {\n        overlayContainer: marker\n    }));\n}, updateCircle);\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;;;AACO,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,mBAAmB,KAAoC,EAAE,GAAG;QAAzC,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,GAAG,SAAS,GAApC;IACxE,MAAM,SAAS,IAAI,oJAAA,CAAA,eAAmB,CAAC,QAAQ;IAC/C,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;QAClD,kBAAkB;IACtB;AACJ,GAAG,8JAAA,CAAA,eAAY", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 561, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/FeatureGroup.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { FeatureGroup as LeafletFeatureGroup } from 'leaflet';\nexport const FeatureGroup = createPathComponent(function createFeatureGroup({ children: _c, ...options }, ctx) {\n    const group = new LeafletFeatureGroup([], options);\n    return createElementObject(group, extendContext(ctx, {\n        layerContainer: group,\n        overlayContainer: group\n    }));\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AACA;;;AACO,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,mBAAmB,KAA4B,EAAE,GAAG;QAAjC,EAAE,UAAU,EAAE,EAAE,GAAG,SAAS,GAA5B;IACxE,MAAM,QAAQ,IAAI,oJAAA,CAAA,eAAmB,CAAC,EAAE,EAAE;IAC1C,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;QACjD,gBAAgB;QAChB,kBAAkB;IACtB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/GeoJSON.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { GeoJSON as LeafletGeoJSON } from 'leaflet';\nexport const GeoJSON = createPathComponent(function createGeoJSON({ data, ...options }, ctx) {\n    const geoJSON = new LeafletGeoJSON(data, options);\n    return createElementObject(geoJSON, extendContext(ctx, {\n        overlayContainer: geoJSON\n    }));\n}, function updateGeoJSON(layer, props, prevProps) {\n    if (props.style !== prevProps.style) {\n        if (props.style == null) {\n            layer.resetStyle();\n        } else {\n            layer.setStyle(props.style);\n        }\n    }\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AACA;;;AACO,MAAM,UAAU,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,cAAc,KAAoB,EAAE,GAAG;QAAzB,EAAE,IAAI,EAAE,GAAG,SAAS,GAApB;IAC9D,MAAM,UAAU,IAAI,oJAAA,CAAA,UAAc,CAAC,MAAM;IACzC,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;QACnD,kBAAkB;IACtB;AACJ,GAAG,SAAS,cAAc,KAAK,EAAE,KAAK,EAAE,SAAS;IAC7C,IAAI,MAAM,KAAK,KAAK,UAAU,KAAK,EAAE;QACjC,IAAI,MAAM,KAAK,IAAI,MAAM;YACrB,MAAM,UAAU;QACpB,OAAO;YACH,MAAM,QAAQ,CAAC,MAAM,KAAK;QAC9B;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 610, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/%40react-leaflet/core/lib/media-overlay.js"], "sourcesContent": ["import { LatLngBounds } from 'leaflet';\nexport function updateMediaOverlay(overlay, props, prevProps) {\n    if (props.bounds instanceof LatLngBounds && props.bounds !== prevProps.bounds) {\n        overlay.setBounds(props.bounds);\n    }\n    if (props.opacity != null && props.opacity !== prevProps.opacity) {\n        overlay.setOpacity(props.opacity);\n    }\n    if (props.zIndex != null && props.zIndex !== prevProps.zIndex) {\n        // @ts-ignore missing in definition but inherited from ImageOverlay\n        overlay.setZIndex(props.zIndex);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,mBAAmB,OAAO,EAAE,KAAK,EAAE,SAAS;IACxD,IAAI,MAAM,MAAM,YAAY,oJAAA,CAAA,eAAY,IAAI,MAAM,MAAM,KAAK,UAAU,MAAM,EAAE;QAC3E,QAAQ,SAAS,CAAC,MAAM,MAAM;IAClC;IACA,IAAI,MAAM,OAAO,IAAI,QAAQ,MAAM,OAAO,KAAK,UAAU,OAAO,EAAE;QAC9D,QAAQ,UAAU,CAAC,MAAM,OAAO;IACpC;IACA,IAAI,MAAM,MAAM,IAAI,QAAQ,MAAM,MAAM,KAAK,UAAU,MAAM,EAAE;QAC3D,mEAAmE;QACnE,QAAQ,SAAS,CAAC,MAAM,MAAM;IAClC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 631, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/ImageOverlay.js"], "sourcesContent": ["import { createElementObject, createLayerComponent, extendContext, updateMediaOverlay } from '@react-leaflet/core';\nimport { LatLngBounds, ImageOverlay as LeafletImageOverlay } from 'leaflet';\nexport const ImageOverlay = createLayerComponent(function createImageOverlay({ bounds, url, ...options }, ctx) {\n    const overlay = new LeafletImageOverlay(url, bounds, options);\n    return createElementObject(overlay, extendContext(ctx, {\n        overlayContainer: overlay\n    }));\n}, function updateImageOverlay(overlay, props, prevProps) {\n    updateMediaOverlay(overlay, props, prevProps);\n    if (props.bounds !== prevProps.bounds) {\n        const bounds = props.bounds instanceof LatLngBounds ? props.bounds : new LatLngBounds(props.bounds);\n        overlay.setBounds(bounds);\n    }\n    if (props.url !== prevProps.url) {\n        overlay.setUrl(props.url);\n    }\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;;;AACO,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,mBAAmB,KAA2B,EAAE,GAAG;QAAhC,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,SAAS,GAA3B;IACzE,MAAM,UAAU,IAAI,oJAAA,CAAA,eAAmB,CAAC,KAAK,QAAQ;IACrD,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;QACnD,kBAAkB;IACtB;AACJ,GAAG,SAAS,mBAAmB,OAAO,EAAE,KAAK,EAAE,SAAS;IACpD,CAAA,GAAA,wKAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,OAAO;IACnC,IAAI,MAAM,MAAM,KAAK,UAAU,MAAM,EAAE;QACnC,MAAM,SAAS,MAAM,MAAM,YAAY,oJAAA,CAAA,eAAY,GAAG,MAAM,MAAM,GAAG,IAAI,oJAAA,CAAA,eAAY,CAAC,MAAM,MAAM;QAClG,QAAQ,SAAS,CAAC;IACtB;IACA,IAAI,MAAM,GAAG,KAAK,UAAU,GAAG,EAAE;QAC7B,QAAQ,MAAM,CAAC,MAAM,GAAG;IAC5B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/LayerGroup.js"], "sourcesContent": ["import { createElementObject, createLayerComponent, extendContext } from '@react-leaflet/core';\nimport { LayerGroup as LeafletLayerGroup } from 'leaflet';\nexport const LayerGroup = createLayerComponent(function createLayerGroup({ children: _c, ...options }, ctx) {\n    const group = new LeafletLayerGroup([], options);\n    return createElementObject(group, extendContext(ctx, {\n        layerContainer: group\n    }));\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AACA;;;AACO,MAAM,aAAa,CAAA,GAAA,+JAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,iBAAiB,KAA4B,EAAE,GAAG;QAAjC,EAAE,UAAU,EAAE,EAAE,GAAG,SAAS,GAA5B;IACrE,MAAM,QAAQ,IAAI,oJAAA,CAAA,aAAiB,CAAC,EAAE,EAAE;IACxC,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;QACjD,gBAAgB;IACpB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 681, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/LayersControl.js"], "sourcesContent": ["import { LeafletContext, createC<PERSON>r<PERSON>omponent, create<PERSON><PERSON><PERSON>H<PERSON>, createElementHook, createElementObject, extendContext, useLeafletContext } from '@react-leaflet/core';\nimport { Control } from 'leaflet';\nimport React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nexport const useLayersControlElement = createElementHook(function createLayersControl({ children: _c, ...options }, ctx) {\n    const control = new Control.Layers(undefined, undefined, options);\n    return createElementObject(control, extendContext(ctx, {\n        layersControl: control\n    }));\n}, function updateLayersControl(control, props, prevProps) {\n    if (props.collapsed !== prevProps.collapsed) {\n        if (props.collapsed === true) {\n            control.collapse();\n        } else {\n            control.expand();\n        }\n    }\n});\nexport const useLayersControl = createControlHook(useLayersControlElement);\n// @ts-ignore\nexport const LayersControl = createContainerComponent(useLayersControl);\nexport function createControlledLayer(addLayerToControl) {\n    return function ControlledLayer(props) {\n        const parentContext = useLeafletContext();\n        const propsRef = useRef(props);\n        const [layer, setLayer] = useState(null);\n        const { layersControl, map } = parentContext;\n        const addLayer = useCallback((layerToAdd)=>{\n            if (layersControl != null) {\n                if (propsRef.current.checked) {\n                    map.addLayer(layerToAdd);\n                }\n                addLayerToControl(layersControl, layerToAdd, propsRef.current.name);\n                setLayer(layerToAdd);\n            }\n        }, [\n            addLayerToControl,\n            layersControl,\n            map\n        ]);\n        const removeLayer = useCallback((layerToRemove)=>{\n            layersControl?.removeLayer(layerToRemove);\n            setLayer(null);\n        }, [\n            layersControl\n        ]);\n        const context = useMemo(()=>{\n            return extendContext(parentContext, {\n                layerContainer: {\n                    addLayer,\n                    removeLayer\n                }\n            });\n        }, [\n            parentContext,\n            addLayer,\n            removeLayer\n        ]);\n        useEffect(()=>{\n            if (layer !== null && propsRef.current !== props) {\n                if (props.checked === true && (propsRef.current.checked == null || propsRef.current.checked === false)) {\n                    map.addLayer(layer);\n                } else if (propsRef.current.checked === true && (props.checked == null || props.checked === false)) {\n                    map.removeLayer(layer);\n                }\n                propsRef.current = props;\n            }\n        });\n        return props.children ? /*#__PURE__*/ React.createElement(LeafletContext, {\n            value: context\n        }, props.children) : null;\n    };\n}\nLayersControl.BaseLayer = createControlledLayer(function addBaseLayer(layersControl, layer, name) {\n    layersControl.addBaseLayer(layer, name);\n});\nLayersControl.Overlay = createControlledLayer(function addOverlay(layersControl, layer, name) {\n    layersControl.addOverlay(layer, name);\n});\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;AACO,MAAM,0BAA0B,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,oBAAoB,KAA4B,EAAE,GAAG;QAAjC,EAAE,UAAU,EAAE,EAAE,GAAG,SAAS,GAA5B;IAClF,MAAM,UAAU,IAAI,oJAAA,CAAA,UAAO,CAAC,MAAM,CAAC,WAAW,WAAW;IACzD,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;QACnD,eAAe;IACnB;AACJ,GAAG,SAAS,oBAAoB,OAAO,EAAE,KAAK,EAAE,SAAS;IACrD,IAAI,MAAM,SAAS,KAAK,UAAU,SAAS,EAAE;QACzC,IAAI,MAAM,SAAS,KAAK,MAAM;YAC1B,QAAQ,QAAQ;QACpB,OAAO;YACH,QAAQ,MAAM;QAClB;IACJ;AACJ;AACO,MAAM,mBAAmB,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD,EAAE;AAE3C,MAAM,gBAAgB,CAAA,GAAA,iKAAA,CAAA,2BAAwB,AAAD,EAAE;AAC/C,SAAS,sBAAsB,iBAAiB;IACnD,OAAO,SAAS,gBAAgB,KAAK;QACjC,MAAM,gBAAgB,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD;QACtC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;QACxB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,MAAM,EAAE,aAAa,EAAE,GAAG,EAAE,GAAG;QAC/B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2EAAE,CAAC;gBAC1B,IAAI,iBAAiB,MAAM;oBACvB,IAAI,SAAS,OAAO,CAAC,OAAO,EAAE;wBAC1B,IAAI,QAAQ,CAAC;oBACjB;oBACA,kBAAkB,eAAe,YAAY,SAAS,OAAO,CAAC,IAAI;oBAClE,SAAS;gBACb;YACJ;0EAAG;YACC;YACA;YACA;SACH;QACD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8EAAE,CAAC;gBAC7B,0BAAA,oCAAA,cAAe,WAAW,CAAC;gBAC3B,SAAS;YACb;6EAAG;YACC;SACH;QACD,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sEAAE;gBACpB,OAAO,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE,eAAe;oBAChC,gBAAgB;wBACZ;wBACA;oBACJ;gBACJ;YACJ;qEAAG;YACC;YACA;YACA;SACH;QACD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+DAAE;gBACN,IAAI,UAAU,QAAQ,SAAS,OAAO,KAAK,OAAO;oBAC9C,IAAI,MAAM,OAAO,KAAK,QAAQ,CAAC,SAAS,OAAO,CAAC,OAAO,IAAI,QAAQ,SAAS,OAAO,CAAC,OAAO,KAAK,KAAK,GAAG;wBACpG,IAAI,QAAQ,CAAC;oBACjB,OAAO,IAAI,SAAS,OAAO,CAAC,OAAO,KAAK,QAAQ,CAAC,MAAM,OAAO,IAAI,QAAQ,MAAM,OAAO,KAAK,KAAK,GAAG;wBAChG,IAAI,WAAW,CAAC;oBACpB;oBACA,SAAS,OAAO,GAAG;gBACvB;YACJ;;QACA,OAAO,MAAM,QAAQ,GAAG,WAAW,GAAG,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,+JAAA,CAAA,iBAAc,EAAE;YACtE,OAAO;QACX,GAAG,MAAM,QAAQ,IAAI;IACzB;AACJ;AACA,cAAc,SAAS,GAAG,sBAAsB,SAAS,aAAa,aAAa,EAAE,KAAK,EAAE,IAAI;IAC5F,cAAc,YAAY,CAAC,OAAO;AACtC;AACA,cAAc,OAAO,GAAG,sBAAsB,SAAS,WAAW,aAAa,EAAE,KAAK,EAAE,IAAI;IACxF,cAAc,UAAU,CAAC,OAAO;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 783, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/MapContainer.js"], "sourcesContent": ["import { LeafletContext, createLeafletContext } from '@react-leaflet/core';\nimport { Map as LeafletMap } from 'leaflet';\nimport React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';\nfunction MapContainerComponent({ bounds, boundsOptions, center, children, className, id, placeholder, style, whenReady, zoom, ...options }, forwardedRef) {\n    const [props] = useState({\n        className,\n        id,\n        style\n    });\n    const [context, setContext] = useState(null);\n    const mapInstanceRef = useRef(undefined);\n    useImperativeHandle(forwardedRef, ()=>context?.map ?? null, [\n        context\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: ref callback\n    const mapRef = useCallback((node)=>{\n        if (node !== null && !mapInstanceRef.current) {\n            const map = new LeafletMap(node, options);\n            mapInstanceRef.current = map;\n            if (center != null && zoom != null) {\n                map.setView(center, zoom);\n            } else if (bounds != null) {\n                map.fitBounds(bounds, boundsOptions);\n            }\n            if (whenReady != null) {\n                map.whenReady(whenReady);\n            }\n            setContext(createLeafletContext(map));\n        }\n    }, []);\n    useEffect(()=>{\n        return ()=>{\n            context?.map.remove();\n        };\n    }, [\n        context\n    ]);\n    const contents = context ? /*#__PURE__*/ React.createElement(LeafletContext, {\n        value: context\n    }, children) : placeholder ?? null;\n    return /*#__PURE__*/ React.createElement(\"div\", {\n        ...props,\n        ref: mapRef\n    }, contents);\n}\nexport const MapContainer = /*#__PURE__*/ forwardRef(MapContainerComponent);\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,SAAS,sBAAsB,KAA2G,EAAE,YAAY;QAAzH,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,SAAS,GAA3G;IAC3B,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrB;QACA;QACA;IACJ;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;qDAAc;gBAAI;mBAAA,CAAA,eAAA,oBAAA,8BAAA,QAAS,GAAG,cAAZ,0BAAA,eAAgB;;oDAAM;QACxD;KACH;IACD,wEAAwE;IACxE,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACxB,IAAI,SAAS,QAAQ,CAAC,eAAe,OAAO,EAAE;gBAC1C,MAAM,MAAM,IAAI,oJAAA,CAAA,MAAU,CAAC,MAAM;gBACjC,eAAe,OAAO,GAAG;gBACzB,IAAI,UAAU,QAAQ,QAAQ,MAAM;oBAChC,IAAI,OAAO,CAAC,QAAQ;gBACxB,OAAO,IAAI,UAAU,MAAM;oBACvB,IAAI,SAAS,CAAC,QAAQ;gBAC1B;gBACA,IAAI,aAAa,MAAM;oBACnB,IAAI,SAAS,CAAC;gBAClB;gBACA,WAAW,CAAA,GAAA,+JAAA,CAAA,uBAAoB,AAAD,EAAE;YACpC;QACJ;oDAAG,EAAE;IACL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACN;mDAAO;oBACH,oBAAA,8BAAA,QAAS,GAAG,CAAC,MAAM;gBACvB;;QACJ;0CAAG;QACC;KACH;IACD,MAAM,WAAW,UAAU,WAAW,GAAG,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,+JAAA,CAAA,iBAAc,EAAE;QACzE,OAAO;IACX,GAAG,YAAY,wBAAA,yBAAA,cAAe;IAC9B,OAAO,WAAW,GAAG,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC5C,GAAG,KAAK;QACR,KAAK;IACT,GAAG;AACP;AACO,MAAM,eAAe,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 851, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/Marker.js"], "sourcesContent": ["import { createElementObject, createLayerComponent, extendContext } from '@react-leaflet/core';\nimport { Marker as LeafletMarker } from 'leaflet';\nexport const Marker = createLayerComponent(function createMarker({ position, ...options }, ctx) {\n    const marker = new LeafletMarker(position, options);\n    return createElementObject(marker, extendContext(ctx, {\n        overlayContainer: marker\n    }));\n}, function updateMarker(marker, props, prevProps) {\n    if (props.position !== prevProps.position) {\n        marker.setLatLng(props.position);\n    }\n    if (props.icon != null && props.icon !== prevProps.icon) {\n        marker.setIcon(props.icon);\n    }\n    if (props.zIndexOffset != null && props.zIndexOffset !== prevProps.zIndexOffset) {\n        marker.setZIndexOffset(props.zIndexOffset);\n    }\n    if (props.opacity != null && props.opacity !== prevProps.opacity) {\n        marker.setOpacity(props.opacity);\n    }\n    if (marker.dragging != null && props.draggable !== prevProps.draggable) {\n        if (props.draggable === true) {\n            marker.dragging.enable();\n        } else {\n            marker.dragging.disable();\n        }\n    }\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AACA;;;AACO,MAAM,SAAS,CAAA,GAAA,+JAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,aAAa,KAAwB,EAAE,GAAG;QAA7B,EAAE,QAAQ,EAAE,GAAG,SAAS,GAAxB;IAC7D,MAAM,SAAS,IAAI,oJAAA,CAAA,SAAa,CAAC,UAAU;IAC3C,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;QAClD,kBAAkB;IACtB;AACJ,GAAG,SAAS,aAAa,MAAM,EAAE,KAAK,EAAE,SAAS;IAC7C,IAAI,MAAM,QAAQ,KAAK,UAAU,QAAQ,EAAE;QACvC,OAAO,SAAS,CAAC,MAAM,QAAQ;IACnC;IACA,IAAI,MAAM,IAAI,IAAI,QAAQ,MAAM,IAAI,KAAK,UAAU,IAAI,EAAE;QACrD,OAAO,OAAO,CAAC,MAAM,IAAI;IAC7B;IACA,IAAI,MAAM,YAAY,IAAI,QAAQ,MAAM,YAAY,KAAK,UAAU,YAAY,EAAE;QAC7E,OAAO,eAAe,CAAC,MAAM,YAAY;IAC7C;IACA,IAAI,MAAM,OAAO,IAAI,QAAQ,MAAM,OAAO,KAAK,UAAU,OAAO,EAAE;QAC9D,OAAO,UAAU,CAAC,MAAM,OAAO;IACnC;IACA,IAAI,OAAO,QAAQ,IAAI,QAAQ,MAAM,SAAS,KAAK,UAAU,SAAS,EAAE;QACpE,IAAI,MAAM,SAAS,KAAK,MAAM;YAC1B,OAAO,QAAQ,CAAC,MAAM;QAC1B,OAAO;YACH,OAAO,QAAQ,CAAC,OAAO;QAC3B;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 891, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/%40react-leaflet/core/lib/dom.js"], "sourcesContent": ["import { DomUtil } from 'leaflet';\nfunction splitClassName(className) {\n    return className.split(' ').filter(Boolean);\n}\nexport function addClassName(element, className) {\n    for (const cls of splitClassName(className)){\n        DomUtil.addClass(element, cls);\n    }\n}\nexport function removeClassName(element, className) {\n    for (const cls of splitClassName(className)){\n        DomUtil.removeClass(element, cls);\n    }\n}\nexport function updateClassName(element, prevClassName, nextClassName) {\n    if (element != null && nextClassName !== prevClassName) {\n        if (prevClassName != null && prevClassName.length > 0) {\n            removeClassName(element, prevClassName);\n        }\n        if (nextClassName != null && nextClassName.length > 0) {\n            addClassName(element, nextClassName);\n        }\n    }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AACA,SAAS,eAAe,SAAS;IAC7B,OAAO,UAAU,KAAK,CAAC,KAAK,MAAM,CAAC;AACvC;AACO,SAAS,aAAa,OAAO,EAAE,SAAS;IAC3C,KAAK,MAAM,OAAO,eAAe,WAAW;QACxC,oJAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,SAAS;IAC9B;AACJ;AACO,SAAS,gBAAgB,OAAO,EAAE,SAAS;IAC9C,KAAK,MAAM,OAAO,eAAe,WAAW;QACxC,oJAAA,CAAA,UAAO,CAAC,WAAW,CAAC,SAAS;IACjC;AACJ;AACO,SAAS,gBAAgB,OAAO,EAAE,aAAa,EAAE,aAAa;IACjE,IAAI,WAAW,QAAQ,kBAAkB,eAAe;QACpD,IAAI,iBAAiB,QAAQ,cAAc,MAAM,GAAG,GAAG;YACnD,gBAAgB,SAAS;QAC7B;QACA,IAAI,iBAAiB,QAAQ,cAAc,MAAM,GAAG,GAAG;YACnD,aAAa,SAAS;QAC1B;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/Pane.js"], "sourcesContent": ["import { LeafletContext, addClassName, useLeafletContext } from '@react-leaflet/core';\nimport React, { forwardRef, useState, useEffect, useImperativeHandle, useMemo } from 'react';\nimport { createPortal } from 'react-dom';\nconst DEFAULT_PANES = [\n    'mapPane',\n    'markerPane',\n    'overlayPane',\n    'popupPane',\n    'shadowPane',\n    'tilePane',\n    'tooltipPane'\n];\nfunction omitPane(obj, pane) {\n    const { [pane]: _p, ...others } = obj;\n    return others;\n}\nfunction createPane(name, props, context) {\n    if (DEFAULT_PANES.indexOf(name) !== -1) {\n        throw new Error(`You must use a unique name for a pane that is not a default Leaflet pane: ${name}`);\n    }\n    if (context.map.getPane(name) != null) {\n        throw new Error(`A pane with this name already exists: ${name}`);\n    }\n    const parentPaneName = props.pane ?? context.pane;\n    const parentPane = parentPaneName ? context.map.getPane(parentPaneName) : undefined;\n    const element = context.map.createPane(name, parentPane);\n    if (props.className != null) {\n        addClassName(element, props.className);\n    }\n    if (props.style != null) {\n        for (const key of Object.keys(props.style)){\n            // @ts-ignore\n            element.style[key] = props.style[key];\n        }\n    }\n    return element;\n}\nfunction PaneComponent(props, forwardedRef) {\n    const [paneName] = useState(props.name);\n    const [paneElement, setPaneElement] = useState(null);\n    useImperativeHandle(forwardedRef, ()=>paneElement, [\n        paneElement\n    ]);\n    const context = useLeafletContext();\n    // biome-ignore lint/correctness/useExhaustiveDependencies: paneName is immutable\n    const newContext = useMemo(()=>({\n            ...context,\n            pane: paneName\n        }), [\n        context\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: lifecycle-only effect\n    useEffect(()=>{\n        setPaneElement(createPane(paneName, props, context));\n        return function removeCreatedPane() {\n            const pane = context.map.getPane(paneName);\n            pane?.remove?.();\n            // @ts-ignore map internals\n            if (context.map._panes != null) {\n                // @ts-ignore map internals\n                context.map._panes = omitPane(context.map._panes, paneName);\n                // @ts-ignore map internals\n                context.map._paneRenderers = omitPane(// @ts-ignore map internals\n                context.map._paneRenderers, paneName);\n            }\n        };\n    }, []);\n    return props.children != null && paneElement != null ? /*#__PURE__*/ createPortal(/*#__PURE__*/ React.createElement(LeafletContext, {\n        value: newContext\n    }, props.children), paneElement) : null;\n}\nexport const Pane = /*#__PURE__*/ forwardRef(PaneComponent);\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;;;;AACA,MAAM,gBAAgB;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,SAAS,SAAS,GAAG,EAAE,IAAI;IACvB,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE,EAAE,GAAG,QAAQ,GAAG;IAClC,OAAO;AACX;AACA,SAAS,WAAW,IAAI,EAAE,KAAK,EAAE,OAAO;IACpC,IAAI,cAAc,OAAO,CAAC,UAAU,CAAC,GAAG;QACpC,MAAM,IAAI,MAAM,AAAC,6EAAiF,OAAL;IACjG;IACA,IAAI,QAAQ,GAAG,CAAC,OAAO,CAAC,SAAS,MAAM;QACnC,MAAM,IAAI,MAAM,AAAC,yCAA6C,OAAL;IAC7D;QACuB;IAAvB,MAAM,iBAAiB,CAAA,cAAA,MAAM,IAAI,cAAV,yBAAA,cAAc,QAAQ,IAAI;IACjD,MAAM,aAAa,iBAAiB,QAAQ,GAAG,CAAC,OAAO,CAAC,kBAAkB;IAC1E,MAAM,UAAU,QAAQ,GAAG,CAAC,UAAU,CAAC,MAAM;IAC7C,IAAI,MAAM,SAAS,IAAI,MAAM;QACzB,CAAA,GAAA,2JAAA,CAAA,eAAY,AAAD,EAAE,SAAS,MAAM,SAAS;IACzC;IACA,IAAI,MAAM,KAAK,IAAI,MAAM;QACrB,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,MAAM,KAAK,EAAE;YACvC,aAAa;YACb,QAAQ,KAAK,CAAC,IAAI,GAAG,MAAM,KAAK,CAAC,IAAI;QACzC;IACJ;IACA,OAAO;AACX;AACA,SAAS,cAAc,KAAK,EAAE,YAAY;IACtC,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,IAAI;IACtC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;6CAAc,IAAI;4CAAa;QAC/C;KACH;IACD,MAAM,UAAU,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD;IAChC,iFAAiF;IACjF,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6CAAE,IAAI,CAAC;gBACxB,GAAG,OAAO;gBACV,MAAM;YACV,CAAC;4CAAG;QACJ;KACH;IACD,iFAAiF;IACjF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,eAAe,WAAW,UAAU,OAAO;YAC3C,OAAO,SAAS;oBAEZ;gBADA,MAAM,OAAO,QAAQ,GAAG,CAAC,OAAO,CAAC;gBACjC,iBAAA,4BAAA,eAAA,KAAM,MAAM,cAAZ,mCAAA,kBAAA;gBACA,2BAA2B;gBAC3B,IAAI,QAAQ,GAAG,CAAC,MAAM,IAAI,MAAM;oBAC5B,2BAA2B;oBAC3B,QAAQ,GAAG,CAAC,MAAM,GAAG,SAAS,QAAQ,GAAG,CAAC,MAAM,EAAE;oBAClD,2BAA2B;oBAC3B,QAAQ,GAAG,CAAC,cAAc,GAAG,SAC7B,QAAQ,GAAG,CAAC,cAAc,EAAE;gBAChC;YACJ;QACJ;kCAAG,EAAE;IACL,OAAO,MAAM,QAAQ,IAAI,QAAQ,eAAe,OAAO,WAAW,GAAG,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,WAAW,GAAG,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,+JAAA,CAAA,iBAAc,EAAE;QAChI,OAAO;IACX,GAAG,MAAM,QAAQ,GAAG,eAAe;AACvC;AACO,MAAM,OAAO,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1015, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/Polygon.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { Polygon as LeafletPolygon } from 'leaflet';\nexport const Polygon = createPathComponent(function createPolygon({ positions, ...options }, ctx) {\n    const polygon = new LeafletPolygon(positions, options);\n    return createElementObject(polygon, extendContext(ctx, {\n        overlayContainer: polygon\n    }));\n}, function updatePolygon(layer, props, prevProps) {\n    if (props.positions !== prevProps.positions) {\n        layer.setLatLngs(props.positions);\n    }\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AACA;;;AACO,MAAM,UAAU,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,cAAc,KAAyB,EAAE,GAAG;QAA9B,EAAE,SAAS,EAAE,GAAG,SAAS,GAAzB;IAC9D,MAAM,UAAU,IAAI,oJAAA,CAAA,UAAc,CAAC,WAAW;IAC9C,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;QACnD,kBAAkB;IACtB;AACJ,GAAG,SAAS,cAAc,KAAK,EAAE,KAAK,EAAE,SAAS;IAC7C,IAAI,MAAM,SAAS,KAAK,UAAU,SAAS,EAAE;QACzC,MAAM,UAAU,CAAC,MAAM,SAAS;IACpC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1039, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/Polyline.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { Polyline as LeafletPolyline } from 'leaflet';\nexport const Polyline = createPathComponent(function createPolyline({ positions, ...options }, ctx) {\n    const polyline = new LeafletPolyline(positions, options);\n    return createElementObject(polyline, extendContext(ctx, {\n        overlayContainer: polyline\n    }));\n}, function updatePolyline(layer, props, prevProps) {\n    if (props.positions !== prevProps.positions) {\n        layer.setLatLngs(props.positions);\n    }\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AACA;;;AACO,MAAM,WAAW,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,eAAe,KAAyB,EAAE,GAAG;QAA9B,EAAE,SAAS,EAAE,GAAG,SAAS,GAAzB;IAChE,MAAM,WAAW,IAAI,oJAAA,CAAA,WAAe,CAAC,WAAW;IAChD,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;QACpD,kBAAkB;IACtB;AACJ,GAAG,SAAS,eAAe,KAAK,EAAE,KAAK,EAAE,SAAS;IAC9C,IAAI,MAAM,SAAS,KAAK,UAAU,SAAS,EAAE;QACzC,MAAM,UAAU,CAAC,MAAM,SAAS;IACpC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1063, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/Popup.js"], "sourcesContent": ["import { createElementObject, createOverlayComponent } from '@react-leaflet/core';\nimport { Popup as LeafletPopup } from 'leaflet';\nimport { useEffect } from 'react';\nexport const Popup = createOverlayComponent(function createPopup(props, context) {\n    const popup = new LeafletPopup(props, context.overlayContainer);\n    return createElementObject(popup, context);\n}, function usePopupLifecycle(element, context, { position }, setOpen) {\n    useEffect(function addPopup() {\n        const { instance } = element;\n        function onPopupOpen(event) {\n            if (event.popup === instance) {\n                instance.update();\n                setOpen(true);\n            }\n        }\n        function onPopupClose(event) {\n            if (event.popup === instance) {\n                setOpen(false);\n            }\n        }\n        context.map.on({\n            popupopen: onPopupOpen,\n            popupclose: onPopupClose\n        });\n        if (context.overlayContainer == null) {\n            // Attach to a Map\n            if (position != null) {\n                instance.setLatLng(position);\n            }\n            instance.openOn(context.map);\n        } else {\n            // Attach to container component\n            context.overlayContainer.bindPopup(instance);\n        }\n        return function removePopup() {\n            context.map.off({\n                popupopen: onPopupOpen,\n                popupclose: onPopupClose\n            });\n            context.overlayContainer?.unbindPopup();\n            context.map.removeLayer(instance);\n        };\n    }, [\n        element,\n        context,\n        setOpen,\n        position\n    ]);\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;;;;AACO,MAAM,QAAQ,CAAA,GAAA,+JAAA,CAAA,yBAAsB,AAAD,EAAE,SAAS,YAAY,KAAK,EAAE,OAAO;IAC3E,MAAM,QAAQ,IAAI,oJAAA,CAAA,QAAY,CAAC,OAAO,QAAQ,gBAAgB;IAC9D,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;AACtC,GAAG,SAAS,kBAAkB,OAAO,EAAE,OAAO,EAAE,KAAY,EAAE,OAAO;QAArB,EAAE,QAAQ,EAAE,GAAZ;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,SAAS,YAAY,KAAK;YACtB,IAAI,MAAM,KAAK,KAAK,UAAU;gBAC1B,SAAS,MAAM;gBACf,QAAQ;YACZ;QACJ;QACA,SAAS,aAAa,KAAK;YACvB,IAAI,MAAM,KAAK,KAAK,UAAU;gBAC1B,QAAQ;YACZ;QACJ;QACA,QAAQ,GAAG,CAAC,EAAE,CAAC;YACX,WAAW;YACX,YAAY;QAChB;QACA,IAAI,QAAQ,gBAAgB,IAAI,MAAM;YAClC,kBAAkB;YAClB,IAAI,YAAY,MAAM;gBAClB,SAAS,SAAS,CAAC;YACvB;YACA,SAAS,MAAM,CAAC,QAAQ,GAAG;QAC/B,OAAO;YACH,gCAAgC;YAChC,QAAQ,gBAAgB,CAAC,SAAS,CAAC;QACvC;QACA,OAAO,SAAS;gBAKZ;YAJA,QAAQ,GAAG,CAAC,GAAG,CAAC;gBACZ,WAAW;gBACX,YAAY;YAChB;aACA,4BAAA,QAAQ,gBAAgB,cAAxB,gDAAA,0BAA0B,WAAW;YACrC,QAAQ,GAAG,CAAC,WAAW,CAAC;QAC5B;IACJ,GAAG;QACC;QACA;QACA;QACA;KACH;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1125, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/Rectangle.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { Rectangle as LeafletRectangle } from 'leaflet';\nexport const Rectangle = createPathComponent(function createRectangle({ bounds, ...options }, ctx) {\n    const rectangle = new LeafletRectangle(bounds, options);\n    return createElementObject(rectangle, extendContext(ctx, {\n        overlayContainer: rectangle\n    }));\n}, function updateRectangle(layer, props, prevProps) {\n    if (props.bounds !== prevProps.bounds) {\n        layer.setBounds(props.bounds);\n    }\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AACA;;;AACO,MAAM,YAAY,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,gBAAgB,KAAsB,EAAE,GAAG;QAA3B,EAAE,MAAM,EAAE,GAAG,SAAS,GAAtB;IAClE,MAAM,YAAY,IAAI,oJAAA,CAAA,YAAgB,CAAC,QAAQ;IAC/C,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;QACrD,kBAAkB;IACtB;AACJ,GAAG,SAAS,gBAAgB,KAAK,EAAE,KAAK,EAAE,SAAS;IAC/C,IAAI,MAAM,MAAM,KAAK,UAAU,MAAM,EAAE;QACnC,MAAM,SAAS,CAAC,MAAM,MAAM;IAChC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1149, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/ScaleControl.js"], "sourcesContent": ["import { createControlComponent } from '@react-leaflet/core';\nimport { Control } from 'leaflet';\nexport const ScaleControl = createControlComponent(function createScaleControl(props) {\n    return new Control.Scale(props);\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,yBAAsB,AAAD,EAAE,SAAS,mBAAmB,KAAK;IAChF,OAAO,IAAI,oJAAA,CAAA,UAAO,CAAC,KAAK,CAAC;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1163, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/SVGOverlay.js"], "sourcesContent": ["import { createElementHook, createElementObject, createLayerHook, updateMediaOverlay } from '@react-leaflet/core';\nimport { SVGOverlay as LeafletSVGOverlay } from 'leaflet';\nimport { forwardRef, useImperativeHandle } from 'react';\nimport { createPortal } from 'react-dom';\nexport const useSVGOverlayElement = createElementHook(function createSVGOverlay(props, context) {\n    const { attributes, bounds, ...options } = props;\n    const container = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    container.setAttribute('xmlns', 'http://www.w3.org/2000/svg');\n    if (attributes != null) {\n        for (const name of Object.keys(attributes)){\n            container.setAttribute(name, attributes[name]);\n        }\n    }\n    const overlay = new LeafletSVGOverlay(container, bounds, options);\n    return createElementObject(overlay, context, container);\n}, updateMediaOverlay);\nexport const useSVGOverlay = createLayerHook(useSVGOverlayElement);\nfunction SVGOverlayComponent({ children, ...options }, forwardedRef) {\n    const { instance, container } = useSVGOverlay(options).current;\n    useImperativeHandle(forwardedRef, ()=>instance);\n    return container == null || children == null ? null : /*#__PURE__*/ createPortal(children, container);\n}\nexport const SVGOverlay = /*#__PURE__*/ forwardRef(SVGOverlayComponent);\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;AACO,MAAM,uBAAuB,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,iBAAiB,KAAK,EAAE,OAAO;IAC1F,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG,SAAS,GAAG;IAC3C,MAAM,YAAY,SAAS,eAAe,CAAC,8BAA8B;IACzE,UAAU,YAAY,CAAC,SAAS;IAChC,IAAI,cAAc,MAAM;QACpB,KAAK,MAAM,QAAQ,OAAO,IAAI,CAAC,YAAY;YACvC,UAAU,YAAY,CAAC,MAAM,UAAU,CAAC,KAAK;QACjD;IACJ;IACA,MAAM,UAAU,IAAI,oJAAA,CAAA,aAAiB,CAAC,WAAW,QAAQ;IACzD,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,SAAS;AACjD,GAAG,wKAAA,CAAA,qBAAkB;AACd,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD,EAAE;AAC7C,SAAS,oBAAoB,KAAwB,EAAE,YAAY;QAAtC,EAAE,QAAQ,EAAE,GAAG,SAAS,GAAxB;IACzB,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,cAAc,SAAS,OAAO;IAC9D,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;mDAAc,IAAI;;IACtC,OAAO,aAAa,QAAQ,YAAY,OAAO,OAAO,WAAW,GAAG,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,UAAU;AAC/F;AACO,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1204, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/%40react-leaflet/core/lib/grid-layer.js"], "sourcesContent": ["export function updateGridLayer(layer, props, prevProps) {\n    const { opacity, zIndex } = props;\n    if (opacity != null && opacity !== prevProps.opacity) {\n        layer.setOpacity(opacity);\n    }\n    if (zIndex != null && zIndex !== prevProps.zIndex) {\n        layer.setZIndex(zIndex);\n    }\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,gBAAgB,KAAK,EAAE,KAAK,EAAE,SAAS;IACnD,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG;IAC5B,IAAI,WAAW,QAAQ,YAAY,UAAU,OAAO,EAAE;QAClD,MAAM,UAAU,CAAC;IACrB;IACA,IAAI,UAAU,QAAQ,WAAW,UAAU,MAAM,EAAE;QAC/C,MAAM,SAAS,CAAC;IACpB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1220, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/TileLayer.js"], "sourcesContent": ["import { createElementObject, createTileLayerComponent, updateGridLayer, withPane } from '@react-leaflet/core';\nimport { TileLayer as LeafletTileLayer } from 'leaflet';\nexport const TileLayer = createTileLayerComponent(function createTileLayer({ url, ...options }, context) {\n    const layer = new LeafletTileLayer(url, withPane(options, context));\n    return createElementObject(layer, context);\n}, function updateTileLayer(layer, props, prevProps) {\n    updateGridLayer(layer, props, prevProps);\n    const { url } = props;\n    if (url != null && url !== prevProps.url) {\n        layer.setUrl(url);\n    }\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;;;AACO,MAAM,YAAY,CAAA,GAAA,+JAAA,CAAA,2BAAwB,AAAD,EAAE,SAAS,gBAAgB,KAAmB,EAAE,OAAO;QAA5B,EAAE,GAAG,EAAE,GAAG,SAAS,GAAnB;IACvE,MAAM,QAAQ,IAAI,oJAAA,CAAA,YAAgB,CAAC,KAAK,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;IAC1D,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;AACtC,GAAG,SAAS,gBAAgB,KAAK,EAAE,KAAK,EAAE,SAAS;IAC/C,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,OAAO;IAC9B,MAAM,EAAE,GAAG,EAAE,GAAG;IAChB,IAAI,OAAO,QAAQ,QAAQ,UAAU,GAAG,EAAE;QACtC,MAAM,MAAM,CAAC;IACjB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1245, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/Tooltip.js"], "sourcesContent": ["import { createElementObject, createOverlayComponent } from '@react-leaflet/core';\nimport { Tooltip as LeafletTooltip } from 'leaflet';\nimport { useEffect } from 'react';\nexport const Tooltip = createOverlayComponent(function createTooltip(props, context) {\n    const tooltip = new LeafletTooltip(props, context.overlayContainer);\n    return createElementObject(tooltip, context);\n}, function useTooltipLifecycle(element, context, { position }, setOpen) {\n    useEffect(function addTooltip() {\n        const container = context.overlayContainer;\n        if (container == null) {\n            return;\n        }\n        const { instance } = element;\n        const onTooltipOpen = (event)=>{\n            if (event.tooltip === instance) {\n                if (position != null) {\n                    instance.setLatLng(position);\n                }\n                instance.update();\n                setOpen(true);\n            }\n        };\n        const onTooltipClose = (event)=>{\n            if (event.tooltip === instance) {\n                setOpen(false);\n            }\n        };\n        container.on({\n            tooltipopen: onTooltipOpen,\n            tooltipclose: onTooltipClose\n        });\n        container.bindTooltip(instance);\n        return function removeTooltip() {\n            container.off({\n                tooltipopen: onTooltipOpen,\n                tooltipclose: onTooltipClose\n            });\n            // @ts-ignore protected property\n            if (container._map != null) {\n                container.unbindTooltip();\n            }\n        };\n    }, [\n        element,\n        context,\n        setOpen,\n        position\n    ]);\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;;;;AACO,MAAM,UAAU,CAAA,GAAA,+JAAA,CAAA,yBAAsB,AAAD,EAAE,SAAS,cAAc,KAAK,EAAE,OAAO;IAC/E,MAAM,UAAU,IAAI,oJAAA,CAAA,UAAc,CAAC,OAAO,QAAQ,gBAAgB;IAClE,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS;AACxC,GAAG,SAAS,oBAAoB,OAAO,EAAE,OAAO,EAAE,KAAY,EAAE,OAAO;QAArB,EAAE,QAAQ,EAAE,GAAZ;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,MAAM,YAAY,QAAQ,gBAAgB;QAC1C,IAAI,aAAa,MAAM;YACnB;QACJ;QACA,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,MAAM;8EAAgB,CAAC;gBACnB,IAAI,MAAM,OAAO,KAAK,UAAU;oBAC5B,IAAI,YAAY,MAAM;wBAClB,SAAS,SAAS,CAAC;oBACvB;oBACA,SAAS,MAAM;oBACf,QAAQ;gBACZ;YACJ;;QACA,MAAM;+EAAiB,CAAC;gBACpB,IAAI,MAAM,OAAO,KAAK,UAAU;oBAC5B,QAAQ;gBACZ;YACJ;;QACA,UAAU,EAAE,CAAC;YACT,aAAa;YACb,cAAc;QAClB;QACA,UAAU,WAAW,CAAC;QACtB,OAAO,SAAS;YACZ,UAAU,GAAG,CAAC;gBACV,aAAa;gBACb,cAAc;YAClB;YACA,gCAAgC;YAChC,IAAI,UAAU,IAAI,IAAI,MAAM;gBACxB,UAAU,aAAa;YAC3B;QACJ;IACJ,GAAG;QACC;QACA;QACA;QACA;KACH;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1310, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/VideoOverlay.js"], "sourcesContent": ["import { createElementObject, createLayerComponent, extendContext, updateMediaOverlay } from '@react-leaflet/core';\nimport { VideoOverlay as LeafletVideoOverlay } from 'leaflet';\nexport const VideoOverlay = createLayerComponent(function createVideoOverlay({ bounds, url, ...options }, ctx) {\n    const overlay = new LeafletVideoOverlay(url, bounds, options);\n    if (options.play === true) {\n        overlay.getElement()?.play();\n    }\n    return createElementObject(overlay, extendContext(ctx, {\n        overlayContainer: overlay\n    }));\n}, function updateVideoOverlay(overlay, props, prevProps) {\n    updateMediaOverlay(overlay, props, prevProps);\n    if (typeof props.url === 'string' && props.url !== prevProps.url) {\n        overlay.setUrl(props.url);\n    }\n    const video = overlay.getElement();\n    if (video != null) {\n        if (props.play === true && !prevProps.play) {\n            video.play();\n        } else if (!props.play && prevProps.play === true) {\n            video.pause();\n        }\n    }\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;;;AACO,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,mBAAmB,KAA2B,EAAE,GAAG;QAAhC,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,SAAS,GAA3B;IACzE,MAAM,UAAU,IAAI,oJAAA,CAAA,eAAmB,CAAC,KAAK,QAAQ;IACrD,IAAI,QAAQ,IAAI,KAAK,MAAM;YACvB;SAAA,sBAAA,QAAQ,UAAU,gBAAlB,0CAAA,oBAAsB,IAAI;IAC9B;IACA,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;QACnD,kBAAkB;IACtB;AACJ,GAAG,SAAS,mBAAmB,OAAO,EAAE,KAAK,EAAE,SAAS;IACpD,CAAA,GAAA,wKAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,OAAO;IACnC,IAAI,OAAO,MAAM,GAAG,KAAK,YAAY,MAAM,GAAG,KAAK,UAAU,GAAG,EAAE;QAC9D,QAAQ,MAAM,CAAC,MAAM,GAAG;IAC5B;IACA,MAAM,QAAQ,QAAQ,UAAU;IAChC,IAAI,SAAS,MAAM;QACf,IAAI,MAAM,IAAI,KAAK,QAAQ,CAAC,UAAU,IAAI,EAAE;YACxC,MAAM,IAAI;QACd,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,UAAU,IAAI,KAAK,MAAM;YAC/C,MAAM,KAAK;QACf;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1348, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/WMSTileLayer.js"], "sourcesContent": ["import { createElementObject, createTileLayerComponent, updateGridLayer, withPane } from '@react-leaflet/core';\nimport { TileLayer } from 'leaflet';\nexport const WMSTileLayer = createTileLayerComponent(function createWMSTileLayer({ eventHandlers: _eh, params = {}, url, ...options }, context) {\n    const layer = new TileLayer.WMS(url, {\n        ...params,\n        ...withPane(options, context)\n    });\n    return createElementObject(layer, context);\n}, function updateWMSTileLayer(layer, props, prevProps) {\n    updateGridLayer(layer, props, prevProps);\n    if (props.params != null && props.params !== prevProps.params) {\n        layer.setParams(props.params);\n    }\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;;;AACO,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,2BAAwB,AAAD,EAAE,SAAS,mBAAmB,KAAoD,EAAE,OAAO;QAA7D,EAAE,eAAe,GAAG,EAAE,SAAS,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,SAAS,GAApD;IAC7E,MAAM,QAAQ,IAAI,oJAAA,CAAA,YAAS,CAAC,GAAG,CAAC,KAAK;QACjC,GAAG,MAAM;QACT,GAAG,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,QAAQ;IACjC;IACA,OAAO,CAAA,GAAA,+JAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;AACtC,GAAG,SAAS,mBAAmB,KAAK,EAAE,KAAK,EAAE,SAAS;IAClD,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,OAAO;IAC9B,IAAI,MAAM,MAAM,IAAI,QAAQ,MAAM,MAAM,KAAK,UAAU,MAAM,EAAE;QAC3D,MAAM,SAAS,CAAC,MAAM,MAAM;IAChC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1375, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/react-leaflet/lib/ZoomControl.js"], "sourcesContent": ["import { createControlComponent } from '@react-leaflet/core';\nimport { Control } from 'leaflet';\nexport const ZoomControl = createControlComponent(function createZoomControl(props) {\n    return new Control.Zoom(props);\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,MAAM,cAAc,CAAA,GAAA,+JAAA,CAAA,yBAAsB,AAAD,EAAE,SAAS,kBAAkB,KAAK;IAC9E,OAAO,IAAI,oJAAA,CAAA,UAAO,CAAC,IAAI,CAAC;AAC5B", "ignoreList": [0], "debugId": null}}]}