{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/lib/api.ts"], "sourcesContent": ["import { EventsData, ViolenceEvent, FilterOptions } from '@/types';\n\n// Configuration for API endpoints\nconst API_CONFIG = {\n  // For development, use mock data from public folder\n  // In production, this would be your backend API URL\n  baseURL: process.env.NODE_ENV === 'production' \n    ? process.env.NEXT_PUBLIC_API_URL || '/api'\n    : '',\n  endpoints: {\n    events: '/events.json',\n    // Future endpoints for real backend\n    // events: '/api/events',\n    // eventById: '/api/events/:id',\n    // divisions: '/api/divisions',\n    // statistics: '/api/statistics'\n  }\n};\n\nclass ApiService {\n  private baseURL: string;\n\n  constructor() {\n    this.baseURL = API_CONFIG.baseURL;\n  }\n\n  /**\n   * Fetch all violence events\n   * In the future, this will connect to a real backend API\n   */\n  async getEvents(): Promise<EventsData> {\n    try {\n      const response = await fetch(`${this.baseURL}${API_CONFIG.endpoints.events}`);\n      \n      if (!response.ok) {\n        throw new Error(`Failed to fetch events: ${response.statusText}`);\n      }\n\n      const data: EventsData = await response.json();\n      return data;\n    } catch (error) {\n      console.error('Error fetching events:', error);\n      throw new Error('Failed to load violence events data');\n    }\n  }\n\n  /**\n   * Get a single event by ID\n   * Currently filters from all events, but will be a separate API call in the future\n   */\n  async getEventById(id: string): Promise<ViolenceEvent | null> {\n    try {\n      const data = await this.getEvents();\n      const event = data.events.find(event => event.id === id);\n      return event || null;\n    } catch (error) {\n      console.error('Error fetching event by ID:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Filter events based on criteria\n   * Currently done client-side, but will be server-side in the future\n   */\n  async getFilteredEvents(filters: Partial<FilterOptions>): Promise<ViolenceEvent[]> {\n    try {\n      const data = await this.getEvents();\n      let filteredEvents = data.events;\n\n      // Apply date range filter\n      if (filters.dateRange?.start || filters.dateRange?.end) {\n        filteredEvents = filteredEvents.filter(event => {\n          const eventDate = new Date(event.date);\n          const start = filters.dateRange?.start;\n          const end = filters.dateRange?.end;\n          \n          if (start && eventDate < start) return false;\n          if (end && eventDate > end) return false;\n          return true;\n        });\n      }\n\n      // Apply political party filter\n      if (filters.politicalParties && filters.politicalParties.length > 0) {\n        filteredEvents = filteredEvents.filter(event =>\n          filters.politicalParties!.includes(event.politicalParty) ||\n          filters.politicalParties!.includes(event.opposingParty)\n        );\n      }\n\n      // Apply division filter\n      if (filters.divisions && filters.divisions.length > 0) {\n        filteredEvents = filteredEvents.filter(event =>\n          filters.divisions!.includes(event.location.division)\n        );\n      }\n\n      // Apply district filter\n      if (filters.districts && filters.districts.length > 0) {\n        filteredEvents = filteredEvents.filter(event =>\n          filters.districts!.includes(event.location.district)\n        );\n      }\n\n      // Apply severity filter\n      if (filters.severityLevels && filters.severityLevels.length > 0) {\n        filteredEvents = filteredEvents.filter(event =>\n          filters.severityLevels!.includes(event.severity)\n        );\n      }\n\n      // Apply verified only filter\n      if (filters.verifiedOnly) {\n        filteredEvents = filteredEvents.filter(event => event.verified);\n      }\n\n      return filteredEvents;\n    } catch (error) {\n      console.error('Error filtering events:', error);\n      throw new Error('Failed to filter events');\n    }\n  }\n\n  /**\n   * Get events within a geographic bounding box\n   * Useful for map viewport optimization\n   */\n  async getEventsInBounds(bounds: {\n    north: number;\n    south: number;\n    east: number;\n    west: number;\n  }): Promise<ViolenceEvent[]> {\n    try {\n      const data = await this.getEvents();\n      return data.events.filter(event => {\n        const { lat, lng } = event.location;\n        return lat <= bounds.north && \n               lat >= bounds.south && \n               lng <= bounds.east && \n               lng >= bounds.west;\n      });\n    } catch (error) {\n      console.error('Error fetching events in bounds:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Get statistics for dashboard/summary views\n   * Will be a separate API endpoint in the future\n   */\n  async getStatistics() {\n    try {\n      const data = await this.getEvents();\n      const events = data.events;\n\n      const stats = {\n        totalEvents: events.length,\n        totalInjured: events.reduce((sum, event) => sum + event.casualties.injured, 0),\n        totalDeaths: events.reduce((sum, event) => sum + event.casualties.dead, 0),\n        byDivision: {} as Record<string, number>,\n        bySeverity: {} as Record<string, number>,\n        byParty: {} as Record<string, number>,\n        recentEvents: events\n          .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())\n          .slice(0, 5)\n      };\n\n      // Calculate division statistics\n      events.forEach(event => {\n        stats.byDivision[event.location.division] = \n          (stats.byDivision[event.location.division] || 0) + 1;\n      });\n\n      // Calculate severity statistics\n      events.forEach(event => {\n        stats.bySeverity[event.severity] = \n          (stats.bySeverity[event.severity] || 0) + 1;\n      });\n\n      // Calculate party statistics\n      events.forEach(event => {\n        stats.byParty[event.politicalParty] = \n          (stats.byParty[event.politicalParty] || 0) + 1;\n      });\n\n      return stats;\n    } catch (error) {\n      console.error('Error fetching statistics:', error);\n      throw new Error('Failed to load statistics');\n    }\n  }\n}\n\n// Export singleton instance\nexport const apiService = new ApiService();\n\n// Export the class for testing or custom instances\nexport default ApiService;\n"], "names": [], "mappings": ";;;;AAMW;;;AAJX,kCAAkC;AAClC,MAAM,aAAa;IACjB,oDAAoD;IACpD,oDAAoD;IACpD,SAAS,sCACL,0BACA;IACJ,WAAW;QACT,QAAQ;IAMV;AACF;AAEA,MAAM;IAOJ;;;GAGC,GACD,MAAM,YAAiC;QACrC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,GAAiB,OAAf,IAAI,CAAC,OAAO,EAA+B,OAA5B,WAAW,SAAS,CAAC,MAAM;YAE1E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,AAAC,2BAA8C,OAApB,SAAS,UAAU;YAChE;YAEA,MAAM,OAAmB,MAAM,SAAS,IAAI;YAC5C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;;GAGC,GACD,MAAM,aAAa,EAAU,EAAiC;QAC5D,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,CAAC,SAAS;YACjC,MAAM,QAAQ,KAAK,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;YACrD,OAAO,SAAS;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACT;IACF;IAEA;;;GAGC,GACD,MAAM,kBAAkB,OAA+B,EAA4B;QACjF,IAAI;gBAKE,oBAA4B;YAJhC,MAAM,OAAO,MAAM,IAAI,CAAC,SAAS;YACjC,IAAI,iBAAiB,KAAK,MAAM;YAEhC,0BAA0B;YAC1B,IAAI,EAAA,qBAAA,QAAQ,SAAS,cAAjB,yCAAA,mBAAmB,KAAK,OAAI,sBAAA,QAAQ,SAAS,cAAjB,0CAAA,oBAAmB,GAAG,GAAE;gBACtD,iBAAiB,eAAe,MAAM,CAAC,CAAA;wBAEvB,oBACF;oBAFZ,MAAM,YAAY,IAAI,KAAK,MAAM,IAAI;oBACrC,MAAM,SAAQ,qBAAA,QAAQ,SAAS,cAAjB,yCAAA,mBAAmB,KAAK;oBACtC,MAAM,OAAM,sBAAA,QAAQ,SAAS,cAAjB,0CAAA,oBAAmB,GAAG;oBAElC,IAAI,SAAS,YAAY,OAAO,OAAO;oBACvC,IAAI,OAAO,YAAY,KAAK,OAAO;oBACnC,OAAO;gBACT;YACF;YAEA,+BAA+B;YAC/B,IAAI,QAAQ,gBAAgB,IAAI,QAAQ,gBAAgB,CAAC,MAAM,GAAG,GAAG;gBACnE,iBAAiB,eAAe,MAAM,CAAC,CAAA,QACrC,QAAQ,gBAAgB,CAAE,QAAQ,CAAC,MAAM,cAAc,KACvD,QAAQ,gBAAgB,CAAE,QAAQ,CAAC,MAAM,aAAa;YAE1D;YAEA,wBAAwB;YACxB,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;gBACrD,iBAAiB,eAAe,MAAM,CAAC,CAAA,QACrC,QAAQ,SAAS,CAAE,QAAQ,CAAC,MAAM,QAAQ,CAAC,QAAQ;YAEvD;YAEA,wBAAwB;YACxB,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;gBACrD,iBAAiB,eAAe,MAAM,CAAC,CAAA,QACrC,QAAQ,SAAS,CAAE,QAAQ,CAAC,MAAM,QAAQ,CAAC,QAAQ;YAEvD;YAEA,wBAAwB;YACxB,IAAI,QAAQ,cAAc,IAAI,QAAQ,cAAc,CAAC,MAAM,GAAG,GAAG;gBAC/D,iBAAiB,eAAe,MAAM,CAAC,CAAA,QACrC,QAAQ,cAAc,CAAE,QAAQ,CAAC,MAAM,QAAQ;YAEnD;YAEA,6BAA6B;YAC7B,IAAI,QAAQ,YAAY,EAAE;gBACxB,iBAAiB,eAAe,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ;YAChE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;;GAGC,GACD,MAAM,kBAAkB,MAKvB,EAA4B;QAC3B,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,CAAC,SAAS;YACjC,OAAO,KAAK,MAAM,CAAC,MAAM,CAAC,CAAA;gBACxB,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,QAAQ;gBACnC,OAAO,OAAO,OAAO,KAAK,IACnB,OAAO,OAAO,KAAK,IACnB,OAAO,OAAO,IAAI,IAClB,OAAO,OAAO,IAAI;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,EAAE;QACX;IACF;IAEA;;;GAGC,GACD,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,CAAC,SAAS;YACjC,MAAM,SAAS,KAAK,MAAM;YAE1B,MAAM,QAAQ;gBACZ,aAAa,OAAO,MAAM;gBAC1B,cAAc,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,UAAU,CAAC,OAAO,EAAE;gBAC5E,aAAa,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,UAAU,CAAC,IAAI,EAAE;gBACxE,YAAY,CAAC;gBACb,YAAY,CAAC;gBACb,SAAS,CAAC;gBACV,cAAc,OACX,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,IACpE,KAAK,CAAC,GAAG;YACd;YAEA,gCAAgC;YAChC,OAAO,OAAO,CAAC,CAAA;gBACb,MAAM,UAAU,CAAC,MAAM,QAAQ,CAAC,QAAQ,CAAC,GACvC,CAAC,MAAM,UAAU,CAAC,MAAM,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;YACvD;YAEA,gCAAgC;YAChC,OAAO,OAAO,CAAC,CAAA;gBACb,MAAM,UAAU,CAAC,MAAM,QAAQ,CAAC,GAC9B,CAAC,MAAM,UAAU,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI;YAC9C;YAEA,6BAA6B;YAC7B,OAAO,OAAO,CAAC,CAAA;gBACb,MAAM,OAAO,CAAC,MAAM,cAAc,CAAC,GACjC,CAAC,MAAM,OAAO,CAAC,MAAM,cAAc,CAAC,IAAI,CAAC,IAAI;YACjD;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,IAAI,MAAM;QAClB;IACF;IA3KA,aAAc;QAFd,+KAAQ,WAAR,KAAA;QAGE,IAAI,CAAC,OAAO,GAAG,WAAW,OAAO;IACnC;AA0KF;AAGO,MAAM,aAAa,IAAI;uCAGf", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/contexts/AppContext.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';\nimport { ViolenceEvent, FilterOptions, EventsData } from '@/types';\nimport { apiService } from '@/lib/api';\n\n// Application state interface\ninterface AppState {\n  // Data\n  events: ViolenceEvent[];\n  filteredEvents: ViolenceEvent[];\n  eventsData: EventsData | null;\n  \n  // UI state\n  selectedEvent: ViolenceEvent | null;\n  loading: boolean;\n  error: string | null;\n  sidebarOpen: boolean;\n  darkMode: boolean;\n  \n  // Filters\n  filters: FilterOptions;\n  \n  // Derived data\n  availableParties: string[];\n  availableDistricts: string[];\n}\n\n// Action types\ntype AppAction =\n  | { type: 'SET_LOADING'; payload: boolean }\n  | { type: 'SET_ERROR'; payload: string | null }\n  | { type: 'SET_EVENTS_DATA'; payload: EventsData }\n  | { type: 'SET_FILTERED_EVENTS'; payload: ViolenceEvent[] }\n  | { type: 'SET_SELECTED_EVENT'; payload: ViolenceEvent | null }\n  | { type: 'SET_SIDEBAR_OPEN'; payload: boolean }\n  | { type: 'SET_DARK_MODE'; payload: boolean }\n  | { type: 'SET_FILTERS'; payload: FilterOptions }\n  | { type: 'TOGGLE_SIDEBAR' }\n  | { type: 'TOGGLE_DARK_MODE' };\n\n// Initial state\nconst initialState: AppState = {\n  events: [],\n  filteredEvents: [],\n  eventsData: null,\n  selectedEvent: null,\n  loading: true,\n  error: null,\n  sidebarOpen: false,\n  darkMode: false,\n  filters: {\n    dateRange: { start: null, end: null },\n    politicalParties: [],\n    divisions: [],\n    districts: [],\n    severityLevels: [],\n    verifiedOnly: false\n  },\n  availableParties: [],\n  availableDistricts: []\n};\n\n// Reducer function\nfunction appReducer(state: AppState, action: AppAction): AppState {\n  switch (action.type) {\n    case 'SET_LOADING':\n      return { ...state, loading: action.payload };\n    \n    case 'SET_ERROR':\n      return { ...state, error: action.payload, loading: false };\n    \n    case 'SET_EVENTS_DATA':\n      const eventsData = action.payload;\n      const availableParties = Array.from(\n        new Set([\n          ...eventsData.events.map(e => e.politicalParty),\n          ...eventsData.events.map(e => e.opposingParty)\n        ])\n      ).filter(Boolean).sort();\n      \n      const availableDistricts = Array.from(\n        new Set(eventsData.events.map(e => e.location.district))\n      ).sort();\n      \n      return {\n        ...state,\n        eventsData,\n        events: eventsData.events,\n        filteredEvents: eventsData.events,\n        availableParties,\n        availableDistricts,\n        loading: false,\n        error: null\n      };\n    \n    case 'SET_FILTERED_EVENTS':\n      return { ...state, filteredEvents: action.payload };\n    \n    case 'SET_SELECTED_EVENT':\n      return { ...state, selectedEvent: action.payload };\n    \n    case 'SET_SIDEBAR_OPEN':\n      return { ...state, sidebarOpen: action.payload };\n    \n    case 'SET_DARK_MODE':\n      return { ...state, darkMode: action.payload };\n    \n    case 'SET_FILTERS':\n      return { ...state, filters: action.payload };\n    \n    case 'TOGGLE_SIDEBAR':\n      return { ...state, sidebarOpen: !state.sidebarOpen };\n    \n    case 'TOGGLE_DARK_MODE':\n      return { ...state, darkMode: !state.darkMode };\n    \n    default:\n      return state;\n  }\n}\n\n// Context\nconst AppContext = createContext<{\n  state: AppState;\n  dispatch: React.Dispatch<AppAction>;\n  actions: {\n    loadEvents: () => Promise<void>;\n    applyFilters: (filters: FilterOptions) => Promise<void>;\n    selectEvent: (event: ViolenceEvent | null) => void;\n    toggleSidebar: () => void;\n    toggleDarkMode: () => void;\n    setSidebarOpen: (open: boolean) => void;\n  };\n} | null>(null);\n\n// Provider component\nexport function AppProvider({ children }: { children: ReactNode }) {\n  const [state, dispatch] = useReducer(appReducer, initialState);\n\n  // Load events from API\n  const loadEvents = async () => {\n    try {\n      dispatch({ type: 'SET_LOADING', payload: true });\n      dispatch({ type: 'SET_ERROR', payload: null });\n      \n      const eventsData = await apiService.getEvents();\n      dispatch({ type: 'SET_EVENTS_DATA', payload: eventsData });\n    } catch (error) {\n      console.error('Failed to load events:', error);\n      dispatch({ \n        type: 'SET_ERROR', \n        payload: error instanceof Error ? error.message : 'Failed to load events' \n      });\n    }\n  };\n\n  // Apply filters and get filtered events\n  const applyFilters = async (filters: FilterOptions) => {\n    try {\n      dispatch({ type: 'SET_FILTERS', payload: filters });\n      \n      const filteredEvents = await apiService.getFilteredEvents(filters);\n      dispatch({ type: 'SET_FILTERED_EVENTS', payload: filteredEvents });\n      \n      // Clear selected event if it's not in filtered results\n      if (state.selectedEvent && !filteredEvents.find(e => e.id === state.selectedEvent!.id)) {\n        dispatch({ type: 'SET_SELECTED_EVENT', payload: null });\n      }\n    } catch (error) {\n      console.error('Failed to apply filters:', error);\n      dispatch({ \n        type: 'SET_ERROR', \n        payload: 'Failed to apply filters' \n      });\n    }\n  };\n\n  // Select an event\n  const selectEvent = (event: ViolenceEvent | null) => {\n    dispatch({ type: 'SET_SELECTED_EVENT', payload: event });\n  };\n\n  // Toggle sidebar\n  const toggleSidebar = () => {\n    dispatch({ type: 'TOGGLE_SIDEBAR' });\n  };\n\n  // Set sidebar open state\n  const setSidebarOpen = (open: boolean) => {\n    dispatch({ type: 'SET_SIDEBAR_OPEN', payload: open });\n  };\n\n  // Toggle dark mode\n  const toggleDarkMode = () => {\n    dispatch({ type: 'TOGGLE_DARK_MODE' });\n    \n    // Persist dark mode preference\n    const newDarkMode = !state.darkMode;\n    localStorage.setItem('darkMode', JSON.stringify(newDarkMode));\n    \n    // Update document class\n    if (newDarkMode) {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n  };\n\n  // Load events on mount\n  useEffect(() => {\n    loadEvents();\n  }, []);\n\n  // Initialize dark mode from localStorage\n  useEffect(() => {\n    const savedDarkMode = localStorage.getItem('darkMode');\n    if (savedDarkMode) {\n      const isDark = JSON.parse(savedDarkMode);\n      dispatch({ type: 'SET_DARK_MODE', payload: isDark });\n      \n      if (isDark) {\n        document.documentElement.classList.add('dark');\n      }\n    }\n  }, []);\n\n  // Actions object\n  const actions = {\n    loadEvents,\n    applyFilters,\n    selectEvent,\n    toggleSidebar,\n    toggleDarkMode,\n    setSidebarOpen\n  };\n\n  return (\n    <AppContext.Provider value={{ state, dispatch, actions }}>\n      {children}\n    </AppContext.Provider>\n  );\n}\n\n// Hook to use the context\nexport function useApp() {\n  const context = useContext(AppContext);\n  if (!context) {\n    throw new Error('useApp must be used within an AppProvider');\n  }\n  return context;\n}\n\nexport default AppContext;\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;;;AAJA;;;AAyCA,gBAAgB;AAChB,MAAM,eAAyB;IAC7B,QAAQ,EAAE;IACV,gBAAgB,EAAE;IAClB,YAAY;IACZ,eAAe;IACf,SAAS;IACT,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QACP,WAAW;YAAE,OAAO;YAAM,KAAK;QAAK;QACpC,kBAAkB,EAAE;QACpB,WAAW,EAAE;QACb,WAAW,EAAE;QACb,gBAAgB,EAAE;QAClB,cAAc;IAChB;IACA,kBAAkB,EAAE;IACpB,oBAAoB,EAAE;AACxB;AAEA,mBAAmB;AACnB,SAAS,WAAW,KAAe,EAAE,MAAiB;IACpD,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,SAAS,OAAO,OAAO;YAAC;QAE7C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,OAAO,OAAO,OAAO;gBAAE,SAAS;YAAM;QAE3D,KAAK;YACH,MAAM,aAAa,OAAO,OAAO;YACjC,MAAM,mBAAmB,MAAM,IAAI,CACjC,IAAI,IAAI;mBACH,WAAW,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,cAAc;mBAC3C,WAAW,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,aAAa;aAC9C,GACD,MAAM,CAAC,SAAS,IAAI;YAEtB,MAAM,qBAAqB,MAAM,IAAI,CACnC,IAAI,IAAI,WAAW,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,QAAQ,IACtD,IAAI;YAEN,OAAO;gBACL,GAAG,KAAK;gBACR;gBACA,QAAQ,WAAW,MAAM;gBACzB,gBAAgB,WAAW,MAAM;gBACjC;gBACA;gBACA,SAAS;gBACT,OAAO;YACT;QAEF,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,gBAAgB,OAAO,OAAO;YAAC;QAEpD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,eAAe,OAAO,OAAO;YAAC;QAEnD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,aAAa,OAAO,OAAO;YAAC;QAEjD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,UAAU,OAAO,OAAO;YAAC;QAE9C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,SAAS,OAAO,OAAO;YAAC;QAE7C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,aAAa,CAAC,MAAM,WAAW;YAAC;QAErD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,UAAU,CAAC,MAAM,QAAQ;YAAC;QAE/C;YACE,OAAO;IACX;AACF;AAEA,UAAU;AACV,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAWrB;AAGH,SAAS,YAAY,KAAqC;QAArC,EAAE,QAAQ,EAA2B,GAArC;;IAC1B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,YAAY;IAEjD,uBAAuB;IACvB,MAAM,aAAa;QACjB,IAAI;YACF,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAK;YAC9C,SAAS;gBAAE,MAAM;gBAAa,SAAS;YAAK;YAE5C,MAAM,aAAa,MAAM,oHAAA,CAAA,aAAU,CAAC,SAAS;YAC7C,SAAS;gBAAE,MAAM;gBAAmB,SAAS;YAAW;QAC1D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS;gBACP,MAAM;gBACN,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA,wCAAwC;IACxC,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAQ;YAEjD,MAAM,iBAAiB,MAAM,oHAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC;YAC1D,SAAS;gBAAE,MAAM;gBAAuB,SAAS;YAAe;YAEhE,uDAAuD;YACvD,IAAI,MAAM,aAAa,IAAI,CAAC,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,aAAa,CAAE,EAAE,GAAG;gBACtF,SAAS;oBAAE,MAAM;oBAAsB,SAAS;gBAAK;YACvD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;gBACP,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEA,kBAAkB;IAClB,MAAM,cAAc,CAAC;QACnB,SAAS;YAAE,MAAM;YAAsB,SAAS;QAAM;IACxD;IAEA,iBAAiB;IACjB,MAAM,gBAAgB;QACpB,SAAS;YAAE,MAAM;QAAiB;IACpC;IAEA,yBAAyB;IACzB,MAAM,iBAAiB,CAAC;QACtB,SAAS;YAAE,MAAM;YAAoB,SAAS;QAAK;IACrD;IAEA,mBAAmB;IACnB,MAAM,iBAAiB;QACrB,SAAS;YAAE,MAAM;QAAmB;QAEpC,+BAA+B;QAC/B,MAAM,cAAc,CAAC,MAAM,QAAQ;QACnC,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;QAEhD,wBAAwB;QACxB,IAAI,aAAa;YACf,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;QACzC,OAAO;YACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;QAC5C;IACF;IAEA,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG,EAAE;IAEL,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,IAAI,eAAe;gBACjB,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,SAAS;oBAAE,MAAM;oBAAiB,SAAS;gBAAO;gBAElD,IAAI,QAAQ;oBACV,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;gBACzC;YACF;QACF;gCAAG,EAAE;IAEL,iBAAiB;IACjB,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,WAAW,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAU;QAAQ;kBACpD;;;;;;AAGP;GAzGgB;KAAA;AA4GT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;uCAQD", "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/node_modules/%40swc/helpers/esm/_define_property.js"], "sourcesContent": ["function _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });\n    } else obj[key] = value;\n\n    return obj;\n}\nexport { _define_property as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,iBAAiB,GAAG,EAAE,GAAG,EAAE,KAAK;IACrC,IAAI,OAAO,KAAK;QACZ,OAAO,cAAc,CAAC,KAAK,KAAK;YAAE,OAAO;YAAO,YAAY;YAAM,cAAc;YAAM,UAAU;QAAK;IACzG,OAAO,GAAG,CAAC,IAAI,GAAG;IAElB,OAAO;AACX", "ignoreList": [0], "debugId": null}}]}