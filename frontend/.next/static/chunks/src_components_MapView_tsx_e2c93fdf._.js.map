{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/components/MapView.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON><PERSON><PERSON>SH_BOUNDS, BANGLADESH_CENTER, ViolenceEvent } from \"@/types\";\nimport { Loader2 } from \"lucide-react\";\nimport { useEffect, useRef, useState } from \"react\";\n\n// Check if we're on the client side\nconst isClient = typeof window !== \"undefined\";\n\n// Only import Leaflet on client side\nlet L: any;\nlet MapContainer: any;\nlet TileLayer: any;\nlet useMap: any;\n\nif (isClient) {\n    L = require(\"leaflet\");\n    require(\"leaflet/dist/leaflet.css\");\n\n    const reactLeaflet = require(\"react-leaflet\");\n    MapContainer = reactLeaflet.MapContainer;\n    TileLayer = reactLeaflet.TileLayer;\n    useMap = reactLeaflet.useMap;\n\n    // Fix for default markers in react-leaflet\n    delete (L.Icon.Default.prototype as any)._getIconUrl;\n    L.Icon.Default.mergeOptions({\n        iconRetinaUrl:\n            \"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png\",\n        iconUrl:\n            \"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png\",\n        shadowUrl:\n            \"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png\",\n    });\n}\n\ninterface MapViewProps {\n    events: ViolenceEvent[];\n    selectedEvent?: ViolenceEvent | null;\n    onEventSelect?: (event: ViolenceEvent | null) => void;\n    className?: string;\n    loading?: boolean;\n}\n\n// Component to handle map bounds and fit to Bangladesh\nfunction MapController({ events }: { events: ViolenceEvent[] }) {\n    const map = useMap ? useMap() : null;\n\n    useEffect(() => {\n        if (events.length > 0) {\n            // Create bounds from all event locations\n            const group = new L.FeatureGroup(\n                events.map((event) =>\n                    L.marker([event.location.lat, event.location.lng])\n                )\n            );\n\n            // Fit map to show all events, with some padding\n            if (group.getBounds().isValid()) {\n                map.fitBounds(group.getBounds(), { padding: [20, 20] });\n            }\n        } else {\n            // Default view of Bangladesh\n            map.setView(BANGLADESH_CENTER, 7);\n        }\n    }, [events, map]);\n\n    return null;\n}\n\nexport default function MapView({\n    events,\n    selectedEvent,\n    onEventSelect,\n    className = \"\",\n    loading = false,\n}: MapViewProps) {\n    const [mapReady, setMapReady] = useState(false);\n    const mapRef = useRef<L.Map | null>(null);\n\n    // Handle map ready state\n    const handleMapReady = () => {\n        setMapReady(true);\n    };\n\n    // Handle event marker click\n    const handleMarkerClick = (event: ViolenceEvent) => {\n        onEventSelect?.(event);\n    };\n\n    // Handle map click (deselect event)\n    const handleMapClick = () => {\n        onEventSelect?.(null);\n    };\n\n    if (loading) {\n        return (\n            <div\n                className={`relative bg-gray-100 dark:bg-gray-800 rounded-lg ${className}`}\n            >\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"flex items-center space-x-2 text-gray-600 dark:text-gray-300\">\n                        <Loader2 className=\"h-6 w-6 animate-spin\" />\n                        <span>Loading map...</span>\n                    </div>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className={`relative ${className}`}>\n            <MapContainer\n                center={BANGLADESH_CENTER}\n                zoom={7}\n                className=\"h-full w-full rounded-lg z-0\"\n                whenReady={handleMapReady}\n                ref={mapRef}\n                onClick={handleMapClick}\n                maxBounds={[\n                    [BANGLADESH_BOUNDS.south - 1, BANGLADESH_BOUNDS.west - 1],\n                    [BANGLADESH_BOUNDS.north + 1, BANGLADESH_BOUNDS.east + 1],\n                ]}\n                minZoom={6}\n                maxZoom={18}\n            >\n                {/* Base tile layer */}\n                <TileLayer\n                    attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n                    url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                />\n\n                {/* Alternative tile layer for better contrast */}\n                {/* <TileLayer\n          attribution='&copy; <a href=\"https://carto.com/attributions\">CARTO</a>'\n          url=\"https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png\"\n        /> */}\n\n                {/* Map controller for bounds management */}\n                <MapController events={events} />\n\n                {/* Event markers */}\n                {mapReady &&\n                    events.map((event) => (\n                        <EventMarker\n                            key={event.id}\n                            event={event}\n                            isSelected={selectedEvent?.id === event.id}\n                            onClick={() => handleMarkerClick(event)}\n                        />\n                    ))}\n            </MapContainer>\n\n            {/* Map overlay info */}\n            <div className=\"absolute top-4 left-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 z-10\">\n                <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\n                    Political Violence Map\n                </div>\n                <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                    {events.length} events displayed\n                </div>\n            </div>\n\n            {/* Legend */}\n            <div className=\"absolute bottom-4 left-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 z-10\">\n                <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Severity Levels\n                </div>\n                <div className=\"space-y-1\">\n                    <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-3 h-3 rounded-full bg-red-500\"></div>\n                        <span className=\"text-xs text-gray-600 dark:text-gray-400\">\n                            High\n                        </span>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-3 h-3 rounded-full bg-orange-500\"></div>\n                        <span className=\"text-xs text-gray-600 dark:text-gray-400\">\n                            Medium\n                        </span>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-3 h-3 rounded-full bg-yellow-500\"></div>\n                        <span className=\"text-xs text-gray-600 dark:text-gray-400\">\n                            Low\n                        </span>\n                    </div>\n                </div>\n            </div>\n\n            {/* Performance indicator for large datasets */}\n            {events.length > 100 && (\n                <div className=\"absolute top-4 right-4 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-lg text-xs z-10\">\n                    Large dataset: {events.length} events\n                </div>\n            )}\n        </div>\n    );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,oCAAoC;AACpC,MAAM,WAAW,aAAkB;AAEnC,qCAAqC;AACrC,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,wCAAc;IACV;;IAGA,MAAM;IACN,eAAe,aAAa,YAAY;IACxC,YAAY,aAAa,SAAS;IAClC,SAAS,aAAa,MAAM;IAE5B,2CAA2C;IAC3C,OAAO,AAAC,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAS,WAAW;IACpD,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;QACxB,eACI;QACJ,SACI;QACJ,WACI;IACR;AACJ;AAUA,uDAAuD;AACvD,SAAS,cAAc,KAAuC;QAAvC,EAAE,MAAM,EAA+B,GAAvC;;IACnB,MAAM,MAAM,SAAS,WAAW;IAEhC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,IAAI,OAAO,MAAM,GAAG,GAAG;gBACnB,yCAAyC;gBACzC,MAAM,QAAQ,IAAI,EAAE,YAAY,CAC5B,OAAO,GAAG;+CAAC,CAAC,QACR,EAAE,MAAM,CAAC;4BAAC,MAAM,QAAQ,CAAC,GAAG;4BAAE,MAAM,QAAQ,CAAC,GAAG;yBAAC;;gBAIzD,gDAAgD;gBAChD,IAAI,MAAM,SAAS,GAAG,OAAO,IAAI;oBAC7B,IAAI,SAAS,CAAC,MAAM,SAAS,IAAI;wBAAE,SAAS;4BAAC;4BAAI;yBAAG;oBAAC;gBACzD;YACJ,OAAO;gBACH,6BAA6B;gBAC7B,IAAI,OAAO,CAAC,wHAAA,CAAA,oBAAiB,EAAE;YACnC;QACJ;kCAAG;QAAC;QAAQ;KAAI;IAEhB,OAAO;AACX;GAvBS;;QACgB;;;KADhB;AAyBM,SAAS,QAAQ,KAMjB;QANiB,EAC5B,MAAM,EACN,aAAa,EACb,aAAa,EACb,YAAY,EAAE,EACd,UAAU,KAAK,EACJ,GANiB;;IAO5B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAgB;IAEpC,yBAAyB;IACzB,MAAM,iBAAiB;QACnB,YAAY;IAChB;IAEA,4BAA4B;IAC5B,MAAM,oBAAoB,CAAC;QACvB,0BAAA,oCAAA,cAAgB;IACpB;IAEA,oCAAoC;IACpC,MAAM,iBAAiB;QACnB,0BAAA,oCAAA,cAAgB;IACpB;IAEA,IAAI,SAAS;QACT,qBACI,6LAAC;YACG,WAAW,AAAC,oDAA6D,OAAV;sBAE/D,cAAA,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6LAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;IAK1B;IAEA,qBACI,6LAAC;QAAI,WAAW,AAAC,YAAqB,OAAV;;0BACxB,6LAAC;gBACG,QAAQ,wHAAA,CAAA,oBAAiB;gBACzB,MAAM;gBACN,WAAU;gBACV,WAAW;gBACX,KAAK;gBACL,SAAS;gBACT,WAAW;oBACP;wBAAC,wHAAA,CAAA,oBAAiB,CAAC,KAAK,GAAG;wBAAG,wHAAA,CAAA,oBAAiB,CAAC,IAAI,GAAG;qBAAE;oBACzD;wBAAC,wHAAA,CAAA,oBAAiB,CAAC,KAAK,GAAG;wBAAG,wHAAA,CAAA,oBAAiB,CAAC,IAAI,GAAG;qBAAE;iBAC5D;gBACD,SAAS;gBACT,SAAS;;kCAGT,6LAAC;wBACG,aAAY;wBACZ,KAAI;;;;;;kCAUR,6LAAC;wBAAc,QAAQ;;;;;;oBAGtB,YACG,OAAO,GAAG,CAAC,CAAC,sBACR,6LAAC;4BAEG,OAAO;4BACP,YAAY,CAAA,0BAAA,oCAAA,cAAe,EAAE,MAAK,MAAM,EAAE;4BAC1C,SAAS,IAAM,kBAAkB;2BAH5B,MAAM,EAAE;;;;;;;;;;;0BAS7B,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;kCAAuD;;;;;;kCAGtE,6LAAC;wBAAI,WAAU;;4BACV,OAAO,MAAM;4BAAC;;;;;;;;;;;;;0BAKvB,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;kCAA4D;;;;;;kCAG3E,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;0CAI/D,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;0CAI/D,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;;YAQtE,OAAO,MAAM,GAAG,qBACb,6LAAC;gBAAI,WAAU;;oBAAiI;oBAC5H,OAAO,MAAM;oBAAC;;;;;;;;;;;;;AAKlD;IAhIwB;MAAA", "debugId": null}}]}