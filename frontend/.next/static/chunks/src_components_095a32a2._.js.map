{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/components/EventPopup.tsx"], "sourcesContent": ["'use client';\n\nimport { ViolenceEvent, SeverityLevel } from '@/types';\nimport { format } from 'date-fns';\nimport { \n  Calendar, \n  MapPin, \n  Users, \n  AlertTriangle, \n  CheckCircle, \n  XCircle,\n  ExternalLink,\n  Heart,\n  Skull\n} from 'lucide-react';\nimport Image from 'next/image';\n\ninterface EventPopupProps {\n  event: ViolenceEvent;\n  showFullDetails?: boolean;\n}\n\n// Severity level styling\nconst SEVERITY_STYLES: Record<SeverityLevel, { bg: string; text: string; border: string }> = {\n  high: { \n    bg: 'bg-red-100 dark:bg-red-900/30', \n    text: 'text-red-800 dark:text-red-200', \n    border: 'border-red-200 dark:border-red-700' \n  },\n  medium: { \n    bg: 'bg-orange-100 dark:bg-orange-900/30', \n    text: 'text-orange-800 dark:text-orange-200', \n    border: 'border-orange-200 dark:border-orange-700' \n  },\n  low: { \n    bg: 'bg-yellow-100 dark:bg-yellow-900/30', \n    text: 'text-yellow-800 dark:text-yellow-200', \n    border: 'border-yellow-200 dark:border-yellow-700' \n  },\n};\n\nexport default function EventPopup({ event, showFullDetails = false }: EventPopupProps) {\n  const severityStyle = SEVERITY_STYLES[event.severity];\n  const eventDate = new Date(event.date);\n  const totalCasualties = event.casualties.injured + event.casualties.dead;\n\n  return (\n    <div className=\"space-y-3 text-sm\">\n      {/* Header */}\n      <div className=\"space-y-2\">\n        <h3 className=\"font-semibold text-gray-900 dark:text-gray-100 leading-tight\">\n          {event.title}\n        </h3>\n        \n        {/* Severity badge */}\n        <div className=\"flex items-center justify-between\">\n          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${severityStyle.bg} ${severityStyle.text} ${severityStyle.border}`}>\n            <AlertTriangle className=\"w-3 h-3 mr-1\" />\n            {event.severity.toUpperCase()}\n          </span>\n          \n          {/* Verification status */}\n          <div className=\"flex items-center space-x-1\">\n            {event.verified ? (\n              <CheckCircle className=\"w-4 h-4 text-green-500\" />\n            ) : (\n              <XCircle className=\"w-4 h-4 text-gray-400\" />\n            )}\n            <span className={`text-xs ${event.verified ? 'text-green-600 dark:text-green-400' : 'text-gray-500'}`}>\n              {event.verified ? 'Verified' : 'Unverified'}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Event image */}\n      {event.imageUrl && (\n        <div className=\"relative w-full h-32 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800\">\n          <Image\n            src={event.imageUrl}\n            alt={event.title}\n            fill\n            className=\"object-cover\"\n            sizes=\"(max-width: 320px) 100vw, 320px\"\n          />\n        </div>\n      )}\n\n      {/* Summary */}\n      <p className=\"text-gray-700 dark:text-gray-300 leading-relaxed\">\n        {event.summary}\n      </p>\n\n      {/* Key details */}\n      <div className=\"space-y-2\">\n        {/* Date and time */}\n        <div className=\"flex items-center space-x-2 text-gray-600 dark:text-gray-400\">\n          <Calendar className=\"w-4 h-4\" />\n          <span>{format(eventDate, 'PPP')}</span>\n          <span className=\"text-xs\">({format(eventDate, 'p')})</span>\n        </div>\n\n        {/* Location */}\n        <div className=\"flex items-center space-x-2 text-gray-600 dark:text-gray-400\">\n          <MapPin className=\"w-4 h-4\" />\n          <span>{event.location.address}</span>\n        </div>\n\n        {/* Political parties involved */}\n        <div className=\"flex items-center space-x-2 text-gray-600 dark:text-gray-400\">\n          <Users className=\"w-4 h-4\" />\n          <span className=\"text-xs\">\n            <span className=\"font-medium\">{event.politicalParty}</span>\n            {event.opposingParty && (\n              <>\n                <span className=\"mx-1\">vs</span>\n                <span className=\"font-medium\">{event.opposingParty}</span>\n              </>\n            )}\n          </span>\n        </div>\n      </div>\n\n      {/* Casualties */}\n      {totalCasualties > 0 && (\n        <div className=\"bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 space-y-2\">\n          <h4 className=\"font-medium text-gray-900 dark:text-gray-100 text-xs uppercase tracking-wide\">\n            Casualties\n          </h4>\n          <div className=\"flex items-center justify-between text-sm\">\n            {event.casualties.injured > 0 && (\n              <div className=\"flex items-center space-x-1 text-orange-600 dark:text-orange-400\">\n                <Heart className=\"w-4 h-4\" />\n                <span>{event.casualties.injured} injured</span>\n              </div>\n            )}\n            {event.casualties.dead > 0 && (\n              <div className=\"flex items-center space-x-1 text-red-600 dark:text-red-400\">\n                <Skull className=\"w-4 h-4\" />\n                <span>{event.casualties.dead} dead</span>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Source and additional details */}\n      <div className=\"pt-2 border-t border-gray-200 dark:border-gray-700\">\n        <div className=\"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400\">\n          <span>Source: {event.source}</span>\n          <button className=\"flex items-center space-x-1 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\">\n            <ExternalLink className=\"w-3 h-3\" />\n            <span>Details</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Administrative info */}\n      {showFullDetails && (\n        <div className=\"pt-2 border-t border-gray-200 dark:border-gray-700 space-y-1\">\n          <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n            <span className=\"font-medium\">Division:</span> {event.location.division}\n          </div>\n          <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n            <span className=\"font-medium\">District:</span> {event.location.district}\n          </div>\n          <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n            <span className=\"font-medium\">Event ID:</span> {event.id}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAfA;;;;;AAsBA,yBAAyB;AACzB,MAAM,kBAAuF;IAC3F,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,QAAQ;IACV;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,QAAQ;IACV;IACA,KAAK;QACH,IAAI;QACJ,MAAM;QACN,QAAQ;IACV;AACF;AAEe,SAAS,WAAW,KAAmD;QAAnD,EAAE,KAAK,EAAE,kBAAkB,KAAK,EAAmB,GAAnD;IACjC,MAAM,gBAAgB,eAAe,CAAC,MAAM,QAAQ,CAAC;IACrD,MAAM,YAAY,IAAI,KAAK,MAAM,IAAI;IACrC,MAAM,kBAAkB,MAAM,UAAU,CAAC,OAAO,GAAG,MAAM,UAAU,CAAC,IAAI;IAExE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX,MAAM,KAAK;;;;;;kCAId,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAW,AAAC,8EAAiG,OAApB,cAAc,EAAE,EAAC,KAAyB,OAAtB,cAAc,IAAI,EAAC,KAAwB,OAArB,cAAc,MAAM;;kDAC3J,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCACxB,MAAM,QAAQ,CAAC,WAAW;;;;;;;0CAI7B,6LAAC;gCAAI,WAAU;;oCACZ,MAAM,QAAQ,iBACb,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;6DAEvB,6LAAC,+MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDAErB,6LAAC;wCAAK,WAAW,AAAC,WAAkF,OAAxE,MAAM,QAAQ,GAAG,uCAAuC;kDACjF,MAAM,QAAQ,GAAG,aAAa;;;;;;;;;;;;;;;;;;;;;;;;YAOtC,MAAM,QAAQ,kBACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAK,MAAM,QAAQ;oBACnB,KAAK,MAAM,KAAK;oBAChB,IAAI;oBACJ,WAAU;oBACV,OAAM;;;;;;;;;;;0BAMZ,6LAAC;gBAAE,WAAU;0BACV,MAAM,OAAO;;;;;;0BAIhB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;0CAAM,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;;;;;;0CACzB,6LAAC;gCAAK,WAAU;;oCAAU;oCAAE,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;oCAAK;;;;;;;;;;;;;kCAIrD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;0CAAM,MAAM,QAAQ,CAAC,OAAO;;;;;;;;;;;;kCAI/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;wCAAK,WAAU;kDAAe,MAAM,cAAc;;;;;;oCAClD,MAAM,aAAa,kBAClB;;0DACE,6LAAC;gDAAK,WAAU;0DAAO;;;;;;0DACvB,6LAAC;gDAAK,WAAU;0DAAe,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ3D,kBAAkB,mBACjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA+E;;;;;;kCAG7F,6LAAC;wBAAI,WAAU;;4BACZ,MAAM,UAAU,CAAC,OAAO,GAAG,mBAC1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;;4CAAM,MAAM,UAAU,CAAC,OAAO;4CAAC;;;;;;;;;;;;;4BAGnC,MAAM,UAAU,CAAC,IAAI,GAAG,mBACvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;;4CAAM,MAAM,UAAU,CAAC,IAAI;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;gCAAK;gCAAS,MAAM,MAAM;;;;;;;sCAC3B,6LAAC;4BAAO,WAAU;;8CAChB,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;YAMX,iCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAgB;4BAAE,MAAM,QAAQ,CAAC,QAAQ;;;;;;;kCAEzE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAgB;4BAAE,MAAM,QAAQ,CAAC,QAAQ;;;;;;;kCAEzE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAgB;4BAAE,MAAM,EAAE;;;;;;;;;;;;;;;;;;;AAMpE;KApIwB", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/components/EventMarker.tsx"], "sourcesContent": ["\"use client\";\n\nimport { SeverityLevel, ViolenceEvent } from \"@/types\";\nimport { useEffect, useRef } from \"react\";\nimport EventPopup from \"./EventPopup\";\n\n// Check if we're on the client side\nconst isClient = typeof window !== \"undefined\";\n\n// Only import Leaflet on client side\nlet L: any;\nlet Marker: any;\nlet Popup: any;\n\nif (isClient) {\n    L = require(\"leaflet\");\n    const reactLeaflet = require(\"react-leaflet\");\n    Marker = reactLeaflet.Marker;\n    Popup = reactLeaflet.Popup;\n}\n\ninterface EventMarkerProps {\n    event: ViolenceEvent;\n    isSelected?: boolean;\n    onClick?: () => void;\n}\n\n// Color mapping for severity levels\nconst SEVERITY_COLORS: Record<SeverityLevel, string> = {\n    high: \"#ef4444\", // red-500\n    medium: \"#f97316\", // orange-500\n    low: \"#eab308\", // yellow-500\n};\n\n// Create custom marker icons based on severity and selection state\nfunction createMarkerIcon(\n    severity: SeverityLevel,\n    isSelected: boolean = false,\n    isRecent: boolean = false\n) {\n    const color = SEVERITY_COLORS[severity];\n    const size = isSelected ? 32 : 24;\n    const pulseClass = isRecent ? \"animate-pulse\" : \"\";\n\n    // Create SVG icon\n    const svgIcon = `\n    <svg width=\"${size}\" height=\"${size}\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n      <defs>\n        <filter id=\"shadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n          <feDropShadow dx=\"0\" dy=\"2\" stdDeviation=\"2\" flood-color=\"rgba(0,0,0,0.3)\"/>\n        </filter>\n      </defs>\n      <circle \n        cx=\"12\" \n        cy=\"12\" \n        r=\"${isSelected ? \"10\" : \"8\"}\" \n        fill=\"${color}\" \n        stroke=\"white\" \n        stroke-width=\"${isSelected ? \"3\" : \"2\"}\"\n        filter=\"url(#shadow)\"\n        class=\"${pulseClass}\"\n      />\n      ${\n          isSelected\n              ? '<circle cx=\"12\" cy=\"12\" r=\"4\" fill=\"white\" opacity=\"0.8\"/>'\n              : \"\"\n      }\n    </svg>\n  `;\n\n    return L.divIcon({\n        html: svgIcon,\n        className: \"custom-marker-icon\",\n        iconSize: [size, size],\n        iconAnchor: [size / 2, size / 2],\n        popupAnchor: [0, -size / 2],\n    });\n}\n\n// Check if event is recent (within last 7 days)\nfunction isRecentEvent(dateString: string): boolean {\n    const eventDate = new Date(dateString);\n    const now = new Date();\n    const daysDiff =\n        (now.getTime() - eventDate.getTime()) / (1000 * 60 * 60 * 24);\n    return daysDiff <= 7;\n}\n\nexport default function EventMarker({\n    event,\n    isSelected = false,\n    onClick,\n}: EventMarkerProps) {\n    const markerRef = useRef<L.Marker | null>(null);\n    const isRecent = isRecentEvent(event.date);\n\n    // Create the appropriate icon based on event properties\n    const icon = createMarkerIcon(event.severity, isSelected, isRecent);\n\n    // Handle marker click\n    const handleClick = () => {\n        onClick?.();\n    };\n\n    // Auto-open popup for selected events\n    useEffect(() => {\n        if (isSelected && markerRef.current) {\n            markerRef.current.openPopup();\n        }\n    }, [isSelected]);\n\n    // Don't render on server side\n    if (!isClient || !Marker || !Popup) {\n        return null;\n    }\n\n    return (\n        <Marker\n            position={[event.location.lat, event.location.lng]}\n            icon={icon}\n            ref={markerRef}\n            eventHandlers={{\n                click: handleClick,\n            }}\n        >\n            <Popup\n                closeButton={true}\n                autoClose={false}\n                closeOnClick={false}\n                className=\"custom-popup\"\n                maxWidth={320}\n                minWidth={280}\n            >\n                <EventPopup event={event} />\n            </Popup>\n        </Marker>\n    );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAMA,oCAAoC;AACpC,MAAM,WAAW,aAAkB;AAEnC,qCAAqC;AACrC,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,wCAAc;IACV;IACA,MAAM;IACN,SAAS,aAAa,MAAM;IAC5B,QAAQ,aAAa,KAAK;AAC9B;AAQA,oCAAoC;AACpC,MAAM,kBAAiD;IACnD,MAAM;IACN,QAAQ;IACR,KAAK;AACT;AAEA,mEAAmE;AACnE,SAAS,iBACL,QAAuB;QACvB,aAAA,iEAAsB,OACtB,WAAA,iEAAoB;IAEpB,MAAM,QAAQ,eAAe,CAAC,SAAS;IACvC,MAAM,OAAO,aAAa,KAAK;IAC/B,MAAM,aAAa,WAAW,kBAAkB;IAEhD,kBAAkB;IAClB,MAAM,UAAU,AAAC,qBACc,OAAjB,MAAK,cASV,OATsB,MAAK,iVAUxB,OADH,aAAa,OAAO,KAAI,sBAGb,OAFR,OAAM,uDAIL,OAFO,aAAa,MAAM,KAAI,qDAKrC,OAHO,YAAW,uBAMrB,OAHG,aACM,+DACA,IACT;IAIH,OAAO,EAAE,OAAO,CAAC;QACb,MAAM;QACN,WAAW;QACX,UAAU;YAAC;YAAM;SAAK;QACtB,YAAY;YAAC,OAAO;YAAG,OAAO;SAAE;QAChC,aAAa;YAAC;YAAG,CAAC,OAAO;SAAE;IAC/B;AACJ;AAEA,gDAAgD;AAChD,SAAS,cAAc,UAAkB;IACrC,MAAM,YAAY,IAAI,KAAK;IAC3B,MAAM,MAAM,IAAI;IAChB,MAAM,WACF,CAAC,IAAI,OAAO,KAAK,UAAU,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAChE,OAAO,YAAY;AACvB;AAEe,SAAS,YAAY,KAIjB;QAJiB,EAChC,KAAK,EACL,aAAa,KAAK,EAClB,OAAO,EACQ,GAJiB;;IAKhC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAmB;IAC1C,MAAM,WAAW,cAAc,MAAM,IAAI;IAEzC,wDAAwD;IACxD,MAAM,OAAO,iBAAiB,MAAM,QAAQ,EAAE,YAAY;IAE1D,sBAAsB;IACtB,MAAM,cAAc;QAChB,oBAAA,8BAAA;IACJ;IAEA,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,IAAI,cAAc,UAAU,OAAO,EAAE;gBACjC,UAAU,OAAO,CAAC,SAAS;YAC/B;QACJ;gCAAG;QAAC;KAAW;IAEf,8BAA8B;IAC9B,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO;QAChC,OAAO;IACX;IAEA,qBACI,6LAAC;QACG,UAAU;YAAC,MAAM,QAAQ,CAAC,GAAG;YAAE,MAAM,QAAQ,CAAC,GAAG;SAAC;QAClD,MAAM;QACN,KAAK;QACL,eAAe;YACX,OAAO;QACX;kBAEA,cAAA,6LAAC;YACG,aAAa;YACb,WAAW;YACX,cAAc;YACd,WAAU;YACV,UAAU;YACV,UAAU;sBAEV,cAAA,6LAAC,mIAAA,CAAA,UAAU;gBAAC,OAAO;;;;;;;;;;;;;;;;AAInC;GAjDwB;KAAA", "debugId": null}}, {"offset": {"line": 618, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Violence%20Map/frontend/src/components/MapView.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON><PERSON>DESH_BOUNDS, BANGLADESH_CENTER, ViolenceEvent } from \"@/types\";\nimport { Loader2 } from \"lucide-react\";\nimport { useEffect, useRef, useState } from \"react\";\n\n// Check if we're on the client side\nconst isClient = typeof window !== \"undefined\";\n\n// Only import Leaflet on client side\nlet L: any;\nlet MapContainer: any;\nlet TileLayer: any;\nlet useMap: any;\nlet EventMarker: any;\n\nif (isClient) {\n    L = require(\"leaflet\");\n    require(\"leaflet/dist/leaflet.css\");\n\n    const reactLeaflet = require(\"react-leaflet\");\n    MapContainer = reactLeaflet.MapContainer;\n    TileLayer = reactLeaflet.TileLayer;\n    useMap = reactLeaflet.useMap;\n\n    // Import EventMarker component\n    EventMarker = require(\"./EventMarker\").default;\n\n    // Fix for default markers in react-leaflet\n    delete (L.Icon.Default.prototype as any)._getIconUrl;\n    L.Icon.Default.mergeOptions({\n        iconRetinaUrl:\n            \"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png\",\n        iconUrl:\n            \"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png\",\n        shadowUrl:\n            \"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png\",\n    });\n}\n\ninterface MapViewProps {\n    events: ViolenceEvent[];\n    selectedEvent?: ViolenceEvent | null;\n    onEventSelect?: (event: ViolenceEvent | null) => void;\n    className?: string;\n    loading?: boolean;\n}\n\n// Component to handle map bounds and fit to Bangladesh\nfunction MapController({ events }: { events: ViolenceEvent[] }) {\n    const map = useMap ? useMap() : null;\n\n    useEffect(() => {\n        if (events.length > 0) {\n            // Create bounds from all event locations\n            const group = new L.FeatureGroup(\n                events.map((event) =>\n                    L.marker([event.location.lat, event.location.lng])\n                )\n            );\n\n            // Fit map to show all events, with some padding\n            if (group.getBounds().isValid()) {\n                map.fitBounds(group.getBounds(), { padding: [20, 20] });\n            }\n        } else {\n            // Default view of Bangladesh\n            map.setView(BANGLADESH_CENTER, 7);\n        }\n    }, [events, map]);\n\n    return null;\n}\n\nexport default function MapView({\n    events,\n    selectedEvent,\n    onEventSelect,\n    className = \"\",\n    loading = false,\n}: MapViewProps) {\n    const [mapReady, setMapReady] = useState(false);\n    const mapRef = useRef<L.Map | null>(null);\n\n    // Handle map ready state\n    const handleMapReady = () => {\n        setMapReady(true);\n    };\n\n    // Handle event marker click\n    const handleMarkerClick = (event: ViolenceEvent) => {\n        onEventSelect?.(event);\n    };\n\n    // Handle map click (deselect event)\n    const handleMapClick = () => {\n        onEventSelect?.(null);\n    };\n\n    // Don't render on server side\n    if (!isClient) {\n        return (\n            <div\n                className={`relative bg-gray-100 dark:bg-gray-800 rounded-lg ${className}`}\n            >\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"flex items-center space-x-2 text-gray-600 dark:text-gray-300\">\n                        <Loader2 className=\"h-6 w-6 animate-spin\" />\n                        <span>Loading map...</span>\n                    </div>\n                </div>\n            </div>\n        );\n    }\n\n    if (loading) {\n        return (\n            <div\n                className={`relative bg-gray-100 dark:bg-gray-800 rounded-lg ${className}`}\n            >\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"flex items-center space-x-2 text-gray-600 dark:text-gray-300\">\n                        <Loader2 className=\"h-6 w-6 animate-spin\" />\n                        <span>Loading map...</span>\n                    </div>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className={`relative ${className}`}>\n            <MapContainer\n                center={BANGLADESH_CENTER}\n                zoom={7}\n                className=\"h-full w-full rounded-lg z-0\"\n                whenReady={handleMapReady}\n                ref={mapRef}\n                onClick={handleMapClick}\n                maxBounds={[\n                    [BANGLADESH_BOUNDS.south - 1, BANGLADESH_BOUNDS.west - 1],\n                    [BANGLADESH_BOUNDS.north + 1, BANGLADESH_BOUNDS.east + 1],\n                ]}\n                minZoom={6}\n                maxZoom={18}\n            >\n                {/* Base tile layer */}\n                <TileLayer\n                    attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n                    url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                />\n\n                {/* Alternative tile layer for better contrast */}\n                {/* <TileLayer\n          attribution='&copy; <a href=\"https://carto.com/attributions\">CARTO</a>'\n          url=\"https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png\"\n        /> */}\n\n                {/* Map controller for bounds management */}\n                <MapController events={events} />\n\n                {/* Event markers */}\n                {mapReady &&\n                    events.map((event) => (\n                        <EventMarker\n                            key={event.id}\n                            event={event}\n                            isSelected={selectedEvent?.id === event.id}\n                            onClick={() => handleMarkerClick(event)}\n                        />\n                    ))}\n            </MapContainer>\n\n            {/* Map overlay info */}\n            <div className=\"absolute top-4 left-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 z-10\">\n                <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\n                    Political Violence Map\n                </div>\n                <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                    {events.length} events displayed\n                </div>\n            </div>\n\n            {/* Legend */}\n            <div className=\"absolute bottom-4 left-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 z-10\">\n                <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                    Severity Levels\n                </div>\n                <div className=\"space-y-1\">\n                    <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-3 h-3 rounded-full bg-red-500\"></div>\n                        <span className=\"text-xs text-gray-600 dark:text-gray-400\">\n                            High\n                        </span>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-3 h-3 rounded-full bg-orange-500\"></div>\n                        <span className=\"text-xs text-gray-600 dark:text-gray-400\">\n                            Medium\n                        </span>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-3 h-3 rounded-full bg-yellow-500\"></div>\n                        <span className=\"text-xs text-gray-600 dark:text-gray-400\">\n                            Low\n                        </span>\n                    </div>\n                </div>\n            </div>\n\n            {/* Performance indicator for large datasets */}\n            {events.length > 100 && (\n                <div className=\"absolute top-4 right-4 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-lg text-xs z-10\">\n                    Large dataset: {events.length} events\n                </div>\n            )}\n        </div>\n    );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,oCAAoC;AACpC,MAAM,WAAW,aAAkB;AAEnC,qCAAqC;AACrC,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,wCAAc;IACV;;IAGA,MAAM;IACN,eAAe,aAAa,YAAY;IACxC,YAAY,aAAa,SAAS;IAClC,SAAS,aAAa,MAAM;IAE5B,+BAA+B;IAC/B,cAAc,8FAAyB,OAAO;IAE9C,2CAA2C;IAC3C,OAAO,AAAC,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAS,WAAW;IACpD,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;QACxB,eACI;QACJ,SACI;QACJ,WACI;IACR;AACJ;AAUA,uDAAuD;AACvD,SAAS,cAAc,KAAuC;QAAvC,EAAE,MAAM,EAA+B,GAAvC;;IACnB,MAAM,MAAM,SAAS,WAAW;IAEhC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,IAAI,OAAO,MAAM,GAAG,GAAG;gBACnB,yCAAyC;gBACzC,MAAM,QAAQ,IAAI,EAAE,YAAY,CAC5B,OAAO,GAAG;+CAAC,CAAC,QACR,EAAE,MAAM,CAAC;4BAAC,MAAM,QAAQ,CAAC,GAAG;4BAAE,MAAM,QAAQ,CAAC,GAAG;yBAAC;;gBAIzD,gDAAgD;gBAChD,IAAI,MAAM,SAAS,GAAG,OAAO,IAAI;oBAC7B,IAAI,SAAS,CAAC,MAAM,SAAS,IAAI;wBAAE,SAAS;4BAAC;4BAAI;yBAAG;oBAAC;gBACzD;YACJ,OAAO;gBACH,6BAA6B;gBAC7B,IAAI,OAAO,CAAC,wHAAA,CAAA,oBAAiB,EAAE;YACnC;QACJ;kCAAG;QAAC;QAAQ;KAAI;IAEhB,OAAO;AACX;GAvBS;;QACgB;;;KADhB;AAyBM,SAAS,QAAQ,KAMjB;QANiB,EAC5B,MAAM,EACN,aAAa,EACb,aAAa,EACb,YAAY,EAAE,EACd,UAAU,KAAK,EACJ,GANiB;;IAO5B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAgB;IAEpC,yBAAyB;IACzB,MAAM,iBAAiB;QACnB,YAAY;IAChB;IAEA,4BAA4B;IAC5B,MAAM,oBAAoB,CAAC;QACvB,0BAAA,oCAAA,cAAgB;IACpB;IAEA,oCAAoC;IACpC,MAAM,iBAAiB;QACnB,0BAAA,oCAAA,cAAgB;IACpB;IAEA,8BAA8B;IAC9B;;IAeA,IAAI,SAAS;QACT,qBACI,6LAAC;YACG,WAAW,AAAC,oDAA6D,OAAV;sBAE/D,cAAA,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6LAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;IAK1B;IAEA,qBACI,6LAAC;QAAI,WAAW,AAAC,YAAqB,OAAV;;0BACxB,6LAAC;gBACG,QAAQ,wHAAA,CAAA,oBAAiB;gBACzB,MAAM;gBACN,WAAU;gBACV,WAAW;gBACX,KAAK;gBACL,SAAS;gBACT,WAAW;oBACP;wBAAC,wHAAA,CAAA,oBAAiB,CAAC,KAAK,GAAG;wBAAG,wHAAA,CAAA,oBAAiB,CAAC,IAAI,GAAG;qBAAE;oBACzD;wBAAC,wHAAA,CAAA,oBAAiB,CAAC,KAAK,GAAG;wBAAG,wHAAA,CAAA,oBAAiB,CAAC,IAAI,GAAG;qBAAE;iBAC5D;gBACD,SAAS;gBACT,SAAS;;kCAGT,6LAAC;wBACG,aAAY;wBACZ,KAAI;;;;;;kCAUR,6LAAC;wBAAc,QAAQ;;;;;;oBAGtB,YACG,OAAO,GAAG,CAAC,CAAC,sBACR,6LAAC;4BAEG,OAAO;4BACP,YAAY,CAAA,0BAAA,oCAAA,cAAe,EAAE,MAAK,MAAM,EAAE;4BAC1C,SAAS,IAAM,kBAAkB;2BAH5B,MAAM,EAAE;;;;;;;;;;;0BAS7B,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;kCAAuD;;;;;;kCAGtE,6LAAC;wBAAI,WAAU;;4BACV,OAAO,MAAM;4BAAC;;;;;;;;;;;;;0BAKvB,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;kCAA4D;;;;;;kCAG3E,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;0CAI/D,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;0CAI/D,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;;YAQtE,OAAO,MAAM,GAAG,qBACb,6LAAC;gBAAI,WAAU;;oBAAiI;oBAC5H,OAAO,MAAM;oBAAC;;;;;;;;;;;;;AAKlD;IAhJwB;MAAA", "debugId": null}}]}