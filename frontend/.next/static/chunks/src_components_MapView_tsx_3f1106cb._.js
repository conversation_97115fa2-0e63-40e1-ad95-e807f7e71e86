(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/MapView.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_react-leaflet_lib_index_2da08ce7.js",
  "static/chunks/node_modules_f30f71b6._.js",
  "static/chunks/src_components_MapView_tsx_e2c93fdf._.js",
  {
    "path": "static/chunks/node_modules_leaflet_dist_leaflet_88e19fd3.css",
    "included": [
      "[project]/node_modules/leaflet/dist/leaflet.css [app-client] (css)"
    ]
  },
  "static/chunks/src_components_MapView_tsx_222d4b14._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/MapView.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);