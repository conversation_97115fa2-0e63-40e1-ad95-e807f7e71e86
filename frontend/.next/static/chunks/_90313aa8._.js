(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/leaflet/dist/leaflet-src.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_leaflet_dist_leaflet-src_e7e140e9.js",
  "static/chunks/node_modules_leaflet_dist_leaflet-src_02be88d9.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/leaflet/dist/leaflet-src.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/react-leaflet/lib/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_6070d793._.js",
  "static/chunks/node_modules_react-leaflet_lib_index_738a345c.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/react-leaflet/lib/index.js [app-client] (ecmascript)");
    });
});
}),
"[project]/src/components/EventMarker.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_ed389632._.js",
  "static/chunks/src_components_9a14eda9._.js",
  "static/chunks/src_components_EventMarker_tsx_02be88d9._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/EventMarker.tsx [app-client] (ecmascript)");
    });
});
}),
}]);