(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/MapView.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_1cef16e5._.js",
  "static/chunks/src_components_MapView_tsx_e2c93fdf._.js",
  "static/chunks/src_components_MapView_tsx_222d4b14._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/MapView.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);