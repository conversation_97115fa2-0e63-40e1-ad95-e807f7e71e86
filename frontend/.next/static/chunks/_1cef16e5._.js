(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/react-leaflet/lib/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_b088d178._.js",
  "static/chunks/node_modules_react-leaflet_lib_index_206e727b.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/react-leaflet/lib/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/src/components/EventMarker.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_fc7a9a02._.js",
  "static/chunks/src_components_1cec800c._.js",
  "static/chunks/src_components_EventMarker_tsx_569c9b32._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/EventMarker.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);